package com.sibuqu.common.service.common;

import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.common.dto.common.CoursePosterMaximSearchDTO;
import com.sibuqu.common.entity.common.CoursePosterMaxim;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sibuqu.common.vo.common.ClassicsHitHomeVO;
import com.sibuqu.common.vo.common.CoursePosterMaximVO;

/**
 * <p>
 * 商品对应的海报箴言列表信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
public interface CoursePosterMaximService extends IService<CoursePosterMaxim> {


    /**
     * @param coursePosterMaximSearchDTO
     * @Return GoodsPosterMaximVO
     * <AUTHOR>
     * @Date 2021/10/27 8:55
     * @Description h5随机取一条课程箴言
     */
    CoursePosterMaximVO randomPosterMaxim(CoursePosterMaximSearchDTO coursePosterMaximSearchDTO, HeaderUserInfo headerUserInfo);

    /**
     * @param coursePosterMaximSearchDTO
     * @Return GoodsPosterMaximVO
     * <AUTHOR>
     * @Date 2021/10/27 8:55
     * @Description pc随机取一条课程箴言
     */
    CoursePosterMaximVO pcRandomPosterMaxim(CoursePosterMaximSearchDTO coursePosterMaximSearchDTO);

    /**
     * @param
     * @Return ClassicsHitHomeVO
     * <AUTHOR>
     * @Date 2022/6/23 16:19
     * @Description 经典撞击首页
     */
    ClassicsHitHomeVO classicsHitHome();

    PageInfoBT<CoursePosterMaximVO> posterMaximPage(CoursePosterMaximSearchDTO dto);
}
