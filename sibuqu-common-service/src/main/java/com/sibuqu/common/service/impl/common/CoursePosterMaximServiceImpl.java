package com.sibuqu.common.service.impl.common;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.common.dto.common.CoursePosterMaximSearchDTO;
import com.sibuqu.common.entity.common.ClassicsHitRecord;
import com.sibuqu.common.entity.common.CoursePosterMaxim;
import com.sibuqu.common.enums.CoursePosterMaximTypeEnum;
import com.sibuqu.common.mapper.common.CoursePosterMaximMapper;
import com.sibuqu.common.service.common.ClassicsHitRecordService;
import com.sibuqu.common.service.common.CoursePosterMaximService;
import com.sibuqu.common.vo.common.ClassicsHitHomeVO;
import com.sibuqu.common.vo.common.CoursePosterMaximVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 商品对应的海报箴言列表信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Service
public class CoursePosterMaximServiceImpl extends ServiceImpl<CoursePosterMaximMapper, CoursePosterMaxim> implements CoursePosterMaximService {

    @Autowired
    ClassicsHitRecordService classicsHitRecordService;

    /**
     * @param coursePosterMaximSearchDTO
     * @Return GoodsPosterMaximVO
     * <AUTHOR>
     * @Date 2021/10/27 8:57
     * @Description 随机获取一条海报箴言
     */
    @Override
    public CoursePosterMaximVO randomPosterMaxim(CoursePosterMaximSearchDTO coursePosterMaximSearchDTO, HeaderUserInfo headerUserInfo) {
        CoursePosterMaximVO vo = new CoursePosterMaximVO();
        CoursePosterMaxim coursePosterMaxim = this.getOne(new LambdaQueryWrapper<CoursePosterMaxim>()
                .eq(ObjectUtil.isNotNull(coursePosterMaximSearchDTO.getCourseId()), CoursePosterMaxim::getCourseId, coursePosterMaximSearchDTO.getCourseId())
                .eq(ObjectUtil.isNotNull(coursePosterMaximSearchDTO.getId()), CoursePosterMaxim::getId, coursePosterMaximSearchDTO.getId())
                .eq(ObjectUtil.isNotEmpty(coursePosterMaximSearchDTO.getSource()) && !Objects.equals(coursePosterMaximSearchDTO.getSource(), "全部经典"), CoursePosterMaxim::getSource, coursePosterMaximSearchDTO.getSource())
                .eq(CoursePosterMaxim::getType, coursePosterMaximSearchDTO.getType())
                .last(" order by rand() limit 1"));
        if (ObjectUtil.isNotNull(coursePosterMaxim)) BeanUtil.copyProperties(coursePosterMaxim, vo);
        // 经典撞击类型下   保存撞击记录
        if (Objects.equals(CoursePosterMaximTypeEnum.CLASSICS_HIT.getCode(), coursePosterMaximSearchDTO.getType())
                && ObjectUtil.isNotNull(coursePosterMaxim)) {
            ClassicsHitRecord classicsHitRecord = new ClassicsHitRecord();
            classicsHitRecord.setHitCourseId(coursePosterMaxim.getCourseId());
            classicsHitRecord.setHitCourseTitle(coursePosterMaxim.getSource());
            classicsHitRecord.setHitContent(coursePosterMaxim.getMaxim());
            if (headerUserInfo != null && headerUserInfo.getId() !=null){
                classicsHitRecord.setUserId(headerUserInfo.getId());
                classicsHitRecord.setUserPhone(headerUserInfo.getPhone());
            }
            classicsHitRecord.setCreateTime(LocalDateTime.now());
            classicsHitRecordService.save(classicsHitRecord);
        }
        return vo;
    }

    /**
     * @param coursePosterMaximSearchDTO
     * @Return GoodsPosterMaximVO
     * <AUTHOR>
     * @Date 2021/10/27 8:57
     * @Description pc随机获取一条海报箴言
     */
    @Override
    public CoursePosterMaximVO pcRandomPosterMaxim(CoursePosterMaximSearchDTO coursePosterMaximSearchDTO) {
        CoursePosterMaximVO vo = new CoursePosterMaximVO();
        CoursePosterMaxim coursePosterMaxim = this.getOne(new LambdaQueryWrapper<CoursePosterMaxim>()
                .eq(ObjectUtil.isNotNull(coursePosterMaximSearchDTO.getCourseId()), CoursePosterMaxim::getCourseId, coursePosterMaximSearchDTO.getCourseId())
                .eq(ObjectUtil.isNotEmpty(coursePosterMaximSearchDTO.getSource()), CoursePosterMaxim::getSource, coursePosterMaximSearchDTO.getSource())
                .eq(CoursePosterMaxim::getType, coursePosterMaximSearchDTO.getType())
                .last(" order by rand() limit 1"));
        if (coursePosterMaxim != null) {
            String maxim = coursePosterMaxim.getMaxim();
            String source = coursePosterMaxim.getSource();
//            if(maxim.contains("——")){
//                maxim = coursePosterMaxim.getMaxim().split("——")[0];
//                source = source + "——" + coursePosterMaxim.getMaxim().split("——")[1];
//            }
            vo.setCourseId(coursePosterMaxim.getCourseId());
            vo.setMaxim(maxim);
            vo.setSource(source);
        }
        return vo;
    }

    /**
     * @param
     * @Return ClassicsHitHomeVO
     * <AUTHOR>
     * @Date 2022/6/23 16:19
     * @Description 经典撞击首页
     */
    @Override
    public ClassicsHitHomeVO classicsHitHome() {
        ClassicsHitHomeVO result = new ClassicsHitHomeVO();
        List<CoursePosterMaxim> coursePosterMaximList = this.list(new LambdaQueryWrapper<CoursePosterMaxim>()
                .eq(CoursePosterMaxim::getType, CoursePosterMaximTypeEnum.CLASSICS_HIT.getCode())
                .groupBy(CoursePosterMaxim::getSource));
        // 获取经典课程集合
        result.setMaximsList(coursePosterMaximList);
        // 获取撞击记录次数
        Integer hitCount = classicsHitRecordService.count();
        result.setHitCount(hitCount);
        return result;
    }

    @Override
    public PageInfoBT<CoursePosterMaximVO> posterMaximPage(CoursePosterMaximSearchDTO dto) {
        Page<CoursePosterMaxim> page = this.page(dto.getPage(), new LambdaQueryWrapper<CoursePosterMaxim>()
                .eq(CoursePosterMaxim::getType, dto.getType())
                .like(ObjectUtil.isNotEmpty(dto.getMaxim()), CoursePosterMaxim::getMaxim, dto.getMaxim()));
        if(CollUtil.isEmpty(page.getRecords())) return PageInfoBT.noData();
        IPage<CoursePosterMaximVO> pageResult = page.convert(item -> {
            CoursePosterMaximVO vo = new CoursePosterMaximVO();
            BeanUtils.copyProperties(item, vo);
            return vo;
        });
        return PageInfoBT.fromPage(pageResult);
    }
}
