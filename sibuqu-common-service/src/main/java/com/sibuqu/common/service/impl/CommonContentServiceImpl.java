package com.sibuqu.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inno.user.bo.UserPointsAddBO;
import com.inno.user.enums.PointsTypeEnum;
import com.sibuqu.base.common.enums.IsDeleteEnum;
import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.utils.BeanUtil;
import com.sibuqu.base.common.utils.FieldUtil;
import com.sibuqu.bidata.dto.UserDoneActionInfoDTO;
import com.sibuqu.common.bo.ContentBO;
import com.sibuqu.common.check.CheckManage;
import com.sibuqu.common.component.*;
import com.sibuqu.common.dto.IdLongDTO;
import com.sibuqu.common.dto.common.SearchCommentAndLikeTotalDTO;
import com.sibuqu.common.dto.common.TopContentDTO;
import com.sibuqu.common.dto.content.CalculateContentRecommendValueDTO;
import com.sibuqu.common.dto.content.CheckUserCanPublishDTO;
import com.sibuqu.common.dto.content.CheckUserSubmitWeekWorkDTO;
import com.sibuqu.common.dto.content.CommonContentInfoDTO;
import com.sibuqu.common.dto.content.CommonContentPageListDTO;
import com.sibuqu.common.dto.content.ContentEditDTO;
import com.sibuqu.common.dto.content.ContentPublishDTO;
import com.sibuqu.common.dto.content.ContentPublishTopicDTO;
import com.sibuqu.common.dto.content.ContentTopDTO;
import com.sibuqu.common.dto.content.GetContentListByDataIdSubDataIdDTO;
import com.sibuqu.common.dto.content.HideContentDTO;
import com.sibuqu.common.dto.content.ManageContentPublishDTO;
import com.sibuqu.common.dto.content.RecommendContentDTO;
import com.sibuqu.common.dto.content.SquareContentListDTO;
import com.sibuqu.common.dto.content.SquareDelSearchHistoryDTO;
import com.sibuqu.common.dto.content.TeamWorkListDTO;
import com.sibuqu.common.dto.content.TipOffContentDTO;
import com.sibuqu.common.dto.content.WeekWorkListDTO;
import com.sibuqu.common.dto.content.WeekWorkPageListDTO;
import com.sibuqu.common.entity.CommonContent;
import com.sibuqu.common.entity.CommonSearchHistory;
import com.sibuqu.common.entity.CommonTipOff;
import com.sibuqu.common.entity.CommonTopic;
import com.sibuqu.common.enums.CommentTypeEnum;
import com.sibuqu.common.enums.CommonContentEnum;
import com.sibuqu.common.enums.DataFlagEnum;
import com.sibuqu.common.enums.LikeTypeEnum;
import com.sibuqu.common.enums.SensitiveWordsTypeEnum;
import com.sibuqu.common.kafka.production.KafkaProduction;
import com.sibuqu.common.mapper.CommonContentMapper;
import com.sibuqu.common.service.CommonContentService;
import com.sibuqu.common.service.CommonSearchHistoryService;
import com.sibuqu.common.service.CommonTipOffService;
import com.sibuqu.common.service.CommonTopicRelService;
import com.sibuqu.common.service.CommonTopicService;
import com.sibuqu.common.service.UserFollowTopicService;
import com.sibuqu.common.service.bo.PageListBO;
import com.sibuqu.common.service.common.CommentRecordService;
import com.sibuqu.common.service.common.SensitiveWordsService;
import com.sibuqu.common.util.AESUtil;
import com.sibuqu.common.util.CommonUtils;
import com.sibuqu.common.vo.common.CommonTopicVO;
import com.sibuqu.common.vo.common.SearchCommentAndLikeTotalVO;
import com.sibuqu.common.vo.common.SensitiveWordsInfoVO;
import com.sibuqu.common.vo.content.CheckUserCanPublishVO;
import com.sibuqu.common.vo.content.CheckUserSubmitWeekWorkVO;
import com.sibuqu.common.vo.content.CommonContentInfoVO;
import com.sibuqu.common.vo.content.CommonContentPageListVO;
import com.sibuqu.common.vo.content.GetContentListByDataIdSubDataIdVO;
import com.sibuqu.common.vo.content.SquareContentItemVO;
import com.sibuqu.common.vo.content.SquareContentListVO;
import com.sibuqu.common.vo.content.SquareSearchHistoryItemVO;
import com.sibuqu.common.vo.content.WeekWorkItemVO;
import com.sibuqu.common.vo.content.WeekWorkPageListVO;
import com.sibuqu.course.vo.courseinfo.CourseInfoVO;
import com.sibuqu.course.vo.goodsinfo.CourseGoodsDetailVO;
import com.sibuqu.order.entity.auth.UserRightsCourse;
import com.sibuqu.starter.redis.headeruserinfo.HeaderUserInfoContext;
import com.sibuqu.starter.redis.headeruserinfo.HeaderUserInfoUtil;
import com.sibuqu.user.enums.TrueOrFalseEnum;
import com.sibuqu.user.vo.api.UserInfoVO;
import com.sibuqu.user.vo.classs.ClassGroupInfoVO;
import com.sibuqu.work.vo.QueryInnoWeeklyInfoByIdListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class CommonContentServiceImpl extends ServiceImpl<CommonContentMapper, CommonContent> implements CommonContentService {

    @Autowired
    private CommentRecordService commentRecordService;

    @Autowired
    private CommonTopicRelService commonTopicRelService;

    @Autowired
    private SensitiveWordsService sensitiveWordsService;

    @Autowired
    private CourseComponent courseComponent;

    @Autowired
    private UserComponent userComponent;

    @Autowired
    private CommonTopicService commonTopicService;

    @Autowired
    private OrderComponent orderComponent;

    @Value("${common.content.topNum:5}")
    private String topNum;

    @Autowired
    private ClassesComponent classesComponent;

    @Autowired
    private CommonSearchHistoryService commonSearchHistoryService;

    @Autowired
    private UserFollowTopicService userFollowTopicService;
    @Autowired
    private CommonTipOffService commonTipOffService;

    @Autowired
    private CheckManage checkManage;

    @Autowired
    private ThreadPoolTaskExecutor quicklyExecutor;

    @Autowired
    private BaseUserComponent baseUserComponent;

    @Autowired
    private WorkComponent workComponent;

    @Autowired
    private KafkaProduction kafkaProduction;
    @Autowired
    private BiDataComponent biDataComponent;

    private final DateTimeFormatter yyyyMMddFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Override
    public PageListBO<CommonContentPageListVO> pageList(CommonContentPageListDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        if (dto.getUserId() != 0 || dto.getTopicId() != 0) {
            dto.setTopFlag(null);
        }
        if (dto.getTopicId() == 0 && dto.getSubDataId() == 0 && dto.getUserId() == 0) {
            dto.setUserId(headerUserInfo.getId());
        }
        Page<CommonContent> pageListVOPage =
                this.baseMapper.pageList(new Page<>(dto.getPageNum(), dto.getPageSize()), dto);
        // 无数据情况
        if (pageListVOPage.getRecords().isEmpty()) {
            PageInfoBT<Object> pageInfoBT = PageInfoBT.from(0, new ArrayList<>());
            PageListBO<CommonContentPageListVO> pageListBO = BeanUtil.copyProperties(pageInfoBT, PageListBO.class);
            setTopicFlag(dto, pageListBO);
            return pageListBO;
        }
        List<Integer> contentIdList = pageListVOPage.getRecords().stream().map(
                CommonContent::getId).map(Long::intValue).collect(Collectors.toList());
        // 点赞评论数据
        Map<Integer, SearchCommentAndLikeTotalVO> likeTotalVOMap =
                searchCommentAndLikeTotalVOMap(CommentTypeEnum.COMMON_CONTENT.getCode(), LikeTypeEnum.COMMON_CONTENT.getCode(),
                        contentIdList, headerUserInfo);
        Map<Long, String> dataNameMap = getDataNameMap(pageListVOPage.getRecords(), dto);
        Map<Long, String> subDataNameMap = getSubDataNameMap(pageListVOPage.getRecords(), dto);
        // 话题数据
        Map<Long, List<CommonTopicVO>> commonTopicRelByDataId = commonTopicRelService.getTopicRelByDataId(contentIdList);
        IPage<CommonContentPageListVO> convert = pageListVOPage.convert(commonContent -> {
            CommonContentPageListVO commonContentPageListVO = new CommonContentPageListVO();
            BeanUtils.copyProperties(commonContent, commonContentPageListVO);
            commonContentPageListVO.setContentList(genContentList(commonContent.getContent()));
            commonContentPageListVO.setCreateTimeStamp(
                    commonContentPageListVO.getCreateTime().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond());
            commonContentPageListVO.setCreateTimeStr(CommonUtils.getCreateTimeStr(commonContentPageListVO.getCreateTime(), null));
            // 点赞评论
            SearchCommentAndLikeTotalVO searchCommentAndLikeTotalVO = likeTotalVOMap.get(commonContentPageListVO.getId().intValue());
            if (searchCommentAndLikeTotalVO != null) {
                commonContentPageListVO.setCommentTotal(searchCommentAndLikeTotalVO.getCommentTotal());
                commonContentPageListVO.setLikeTotal(searchCommentAndLikeTotalVO.getLikeTotal());
                commonContentPageListVO.setLikeFlag(searchCommentAndLikeTotalVO.getLikeFlag());
            }
            commonContentPageListVO.setDataName(dataNameMap.get(commonContentPageListVO.getId()));
            commonContentPageListVO.setSubDataName(subDataNameMap.get(commonContentPageListVO.getId()));
            // 话题
            List<CommonTopicVO> commonTopicVOS = commonTopicRelByDataId.get(commonContent.getId());
            if (CollectionUtil.isNotEmpty(commonTopicVOS)) {
                commonContentPageListVO.setTopicVOS(commonTopicVOS);
            }
            return commonContentPageListVO;
        });
        PageInfoBT<CommonContentPageListVO> page = PageInfoBT.fromPage(convert);
        PageListBO<CommonContentPageListVO> pageListBO = BeanUtil.copyProperties(page, PageListBO.class);
        setTopicFlag(dto, pageListBO);
        return pageListBO;
    }

    private void setTopicFlag(CommonContentPageListDTO dto, PageListBO<CommonContentPageListVO> pageListBO) {
        if (Objects.nonNull(dto.getTopicId())) {
            CommonTopic commonTopic = commonTopicService.getById(dto.getTopicId());
            LocalDateTime now = LocalDateTime.now();
            if (Objects.isNull(commonTopic) || !DataFlagEnum.SHOW.getCode().equals(commonTopic.getDataFlag())
                    || (Objects.nonNull(commonTopic.getBeginTime()) && (commonTopic.getBeginTime().isAfter(now) || commonTopic.getEndTime().isBefore(now)))) {
                pageListBO.setTopicFlag(0);
            }
        }
    }

    private Map<Long, String> getSubDataNameMap(List<CommonContent> records, CommonContentPageListDTO dto) {
        if (dto.getTopicId() == null) {
            return Collections.emptyMap();
        }
        List<Integer> classGroupIdList = new ArrayList<>();
        // 根据类型查询不同的关联名称
        for (CommonContent commonContent : records) {
            if (commonContent.getDataType().equals(1) && commonContent.getSubDataId() != null) {
                classGroupIdList.add(commonContent.getSubDataId().intValue());
            }
        }
        if (CollectionUtil.isEmpty(classGroupIdList)) {
            return Collections.emptyMap();
        }
        Map<Long, String> subDataNameMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(classGroupIdList)) {
            Map<Integer, String> classGroupNameMap = classesComponent.queryClassesNameByClassesId(classGroupIdList);
            for (CommonContent commonContent : records) {
                if (commonContent.getDataType().equals(1) && commonContent.getSubDataId() != null) {
                    subDataNameMap.put(commonContent.getId(), classGroupNameMap.get(commonContent.getSubDataId().intValue()));
                }
            }
        }
        return subDataNameMap;
    }

    private Map<Long, String> getDataNameMap(List<CommonContent> records, CommonContentPageListDTO dto) {
        if (dto.getTopicId() == null) {
            return Collections.emptyMap();
        }
        List<Integer> courseIdList = new ArrayList<>();
        // 根据类型查询不同的关联名称
        for (CommonContent commonContent : records) {
            if (commonContent.getDataType().equals(1)) {
                courseIdList.add(commonContent.getDataId().intValue());
            }
        }
        Map<Long, String> dataNameMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(courseIdList)) {
            Map<Integer, String> courseNameMap =
                    courseComponent.getCourseInfoList(courseIdList).stream().collect(
                            Collectors.toMap(CourseInfoVO::getId, CourseInfoVO::getCourseTitle));
            for (CommonContent commonContent : records) {
                if (commonContent.getDataType().equals(1)) {
                    dataNameMap.put(commonContent.getId(), courseNameMap.get(commonContent.getDataId().intValue()));
                }
            }
        }
        return dataNameMap;

    }

    @Override
    public List<CommonContentPageListVO> topContentList(TopContentDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        List<CommonContent> commonContents = this.list(new LambdaQueryWrapper<CommonContent>().eq(CommonContent::getDataType, dto.getDataType())
                .eq(CommonContent::getDataId, dto.getDataId())
                .eq(CommonContent::getSubDataId, dto.getSubDataId())
                .eq(CommonContent::getTopFlag, TrueOrFalseEnum.TRUE.getCode())
                .eq(CommonContent::getShowStatus, TrueOrFalseEnum.TRUE.getCode())
                .eq(CommonContent::getDeleteFlag, TrueOrFalseEnum.FALSE.getCode())
                .orderByDesc(CommonContent::getTopTime)
                .last("limit " + topNum));
        if (CollectionUtil.isEmpty(commonContents)) {
            return Collections.emptyList();
        }
        List<Integer> dataIdList = new ArrayList<>();
        for (CommonContent commonContent : commonContents) {
            dataIdList.add(commonContent.getId().intValue());
        }
        // 点赞评论数据
        Map<Integer, SearchCommentAndLikeTotalVO> likeTotalVOMap =
                searchCommentAndLikeTotalVOMap(CommentTypeEnum.COMMON_CONTENT.getCode(), LikeTypeEnum.COMMON_CONTENT.getCode(),
                        dataIdList, headerUserInfo);
        // 话题数据
        Map<Long, List<CommonTopicVO>> commonTopicRelByDataId = commonTopicRelService.getTopicRelByDataId(dataIdList);
        return commonContents.stream().map(commonContent -> {
            CommonContentPageListVO commonContentPageListVO = new CommonContentPageListVO();
            BeanUtils.copyProperties(commonContent, commonContentPageListVO);
            commonContentPageListVO.setContentList(genContentList(commonContent.getContent()));
            commonContentPageListVO.setCreateTimeStamp(
                    commonContent.getCreateTime().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond());
            commonContentPageListVO.setCreateTimeStr(CommonUtils.getCreateTimeStr(commonContent.getCreateTime(), null));
            // 点赞评论
            SearchCommentAndLikeTotalVO searchCommentAndLikeTotalVO = likeTotalVOMap.get(commonContent.getId().intValue());
            if (searchCommentAndLikeTotalVO != null) {
                commonContentPageListVO.setCommentTotal(searchCommentAndLikeTotalVO.getCommentTotal());
                commonContentPageListVO.setLikeTotal(searchCommentAndLikeTotalVO.getLikeTotal());
                commonContentPageListVO.setLikeFlag(searchCommentAndLikeTotalVO.getLikeFlag());
            }
            // 话题
            List<CommonTopicVO> commonTopicVOS = commonTopicRelByDataId.get(commonContent.getId());
            if (CollectionUtil.isNotEmpty(commonTopicVOS)) {
                commonContentPageListVO.setTopicVOS(commonTopicVOS);
            }
            return commonContentPageListVO;
        }).collect(Collectors.toList());

    }

    @Override
    public CommonContentInfoVO contentInfo(CommonContentInfoDTO dto) {
        CommonContent commonContent = this.getById(dto.getId());
        if (commonContent == null) return null;
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        // 点赞评论数据
        Map<Integer, SearchCommentAndLikeTotalVO> likeTotalVOMap =
                searchCommentAndLikeTotalVOMap(CommentTypeEnum.COMMON_CONTENT.getCode(), LikeTypeEnum.COMMON_CONTENT.getCode(),
                        Collections.singletonList(dto.getId().intValue()), headerUserInfo);
        // 话题数据
        Map<Long, List<CommonTopicVO>> commonTopicRelByDataId = commonTopicRelService.getTopicRelByDataId(
                Collections.singletonList(dto.getId().intValue()));
        SearchCommentAndLikeTotalVO searchCommentAndLikeTotalVO = likeTotalVOMap.get(dto.getId().intValue());
        CommonContentInfoVO vo = new CommonContentInfoVO();
        BeanUtils.copyProperties(commonContent, vo);
        vo.setContentList(genContentList(commonContent.getContent()));
        vo.setCreateTimeStamp(
                commonContent.getCreateTime().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond());
        vo.setCreateTimeStr(CommonUtils.getCreateTimeStr(commonContent.getUpdateTime(), null));
        // 点赞评论
        if (searchCommentAndLikeTotalVO != null) {
            vo.setCommentTotal(searchCommentAndLikeTotalVO.getCommentTotal());
            vo.setLikeTotal(searchCommentAndLikeTotalVO.getLikeTotal());
            vo.setLikeFlag(searchCommentAndLikeTotalVO.getLikeFlag());
        }
        // 话题
        List<CommonTopicVO> commonTopicVOS = commonTopicRelByDataId.get(commonContent.getId());
        if (CollectionUtil.isNotEmpty(commonTopicVOS)) {
            vo.setTopicVOS(commonTopicVOS);
        }
        // 是否关注过当前发布人
        if (!headerUserInfo.getId().equals(commonContent.getUserId())) {
            Integer followType = userComponent.batchGetUserFollow(headerUserInfo.getId(), commonContent.getUserId());
            vo.setFollowType(followType);
        }
        vo.setSpecialUserFlag(courseComponent.checkCourseSpecialUserList(7, commonContent.getUserId(), null));
        if (CommonContentEnum.DataType.INNO_WEEK_WORK.getCode().equals(commonContent.getDataType())) {
            // 查询周作业信息
            QueryInnoWeeklyInfoByIdListVO queryInnoWeeklyInfoByIdListVO = workComponent.queryInnoWeeklyInfoByIdList(commonContent.getSubDataId());
            if (Objects.nonNull(queryInnoWeeklyInfoByIdListVO)) {
                vo.setWeekWorkTitle(queryInnoWeeklyInfoByIdListVO.getTitle());
                vo.setWeekWorkDesc(queryInnoWeeklyInfoByIdListVO.getDescription());
            }
            String tribeName = classesComponent.getTribeNameByCourseIdUserId(commonContent.getDataId().intValue(), commonContent.getUserId());
            vo.setTribeName(tribeName);
        }
        return vo;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long contentPublish(ContentPublishDTO dto) {
        if (CollUtil.isEmpty(dto.getContentList())) {
            throw new BusinessException("内容列表不能为空");
        }
        dto.getContentList().forEach(contentBO -> {
            if (StrUtil.isBlank(contentBO.getContent())) {
                throw new BusinessException("内容不能为空");
            }
        });
        // 润泽园敏感词
        checkContentLegal(dto.getContentList());
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        CommonContent content = BeanUtil.copyProperties(dto, CommonContent.class);
        content.setContent(getContentJson(dto.getContentList()));
        content.setContentType(getContentType(dto.getContentList()));
        content.setPicFlag(getPicFlag(dto.getContentList()));
        content.setUserId(headerUserInfo.getId());
        content.setUserPhone(headerUserInfo.getPhone());
        if (StrUtil.isNotBlank(content.getUserPhone()) && Pattern.matches("\\d+", content.getUserPhone())) {
            content.setUserPhone(AESUtil.encrypt(content.getUserPhone()));
        }
        content.setUserName(headerUserInfo.getUserFullName());
        setUserNameForClass(dto, headerUserInfo, content);
        LocalDateTime now = LocalDateTime.now();
        FieldUtil.setCreateAndUpdate(content, headerUserInfo, now);
        if (dto.getBeginTime() != null && dto.getBeginTime().isAfter(now)) {
            content.setShowStatus(0);
        }
        save(content);
        // 关联话题
        linkTopic(dto, content);
        // 校验文字
        CompletableFuture.runAsync(() -> checkContent(dto), quicklyExecutor).exceptionally((e) -> {
            log.error("contentPublish checkContent error:{}", e.getMessage(), e);
            content.setCheckStatus(CommonContentEnum.CheckStatus.NOT_PASS.getCode());
            content.setUpdateTime(LocalDateTime.now());
            updateById(content);
            return null;
        });
        // 校验图片
        CompletableFuture.runAsync(() -> checkPic(dto), quicklyExecutor).exceptionally((e) -> {
            log.error("contentPublish checkPic error:{}", e.getMessage(), e);
            content.setCheckStatus(CommonContentEnum.CheckStatus.NOT_PASS.getCode());
            content.setUpdateTime(LocalDateTime.now());
            updateById(content);
            return null;
        });
        if (CommonContentEnum.DataType.INNO_WEEK_WORK.getCode().equals(content.getDataType())) {
            QueryInnoWeeklyInfoByIdListVO queryInnoWeeklyInfoByIdListVO = workComponent.queryInnoWeeklyInfoByIdList(content.getSubDataId());
            UserPointsAddBO userPointsAddBO = new UserPointsAddBO();
            userPointsAddBO.setUserId(headerUserInfo.getId());
            userPointsAddBO.setCourseId(content.getDataId().intValue());
            userPointsAddBO.setSceneName(queryInnoWeeklyInfoByIdListVO.getTitle());
            userPointsAddBO.setRequestId(String.format("weekWork_%s_%s_%s_%s", headerUserInfo.getId(), content.getDataId(),
                    content.getSubDataId(), queryInnoWeeklyInfoByIdListVO.getWorkBeginDatetime().format(yyyyMMddFormatter)));
            userPointsAddBO.setScore(queryInnoWeeklyInfoByIdListVO.getUserPoints().intValue());
            userPointsAddBO.setPointsTypeEnum(PointsTypeEnum.SUBMIT_WEEKLY_HOMEWORK);
            kafkaProduction.send(userPointsAddBO);
            try{
                UserDoneActionInfoDTO userDoneActionInfoDTO = new UserDoneActionInfoDTO();
                userDoneActionInfoDTO.setUserId(userPointsAddBO.getUserId());
                userDoneActionInfoDTO.setCourseId(userPointsAddBO.getCourseId());
                userDoneActionInfoDTO.setDataId(queryInnoWeeklyInfoByIdListVO.getId().intValue());
                userDoneActionInfoDTO.setDataType(2);
                userDoneActionInfoDTO.setDataName(queryInnoWeeklyInfoByIdListVO.getName());
                userDoneActionInfoDTO.setDataNotes(queryInnoWeeklyInfoByIdListVO.getTitle());
                userDoneActionInfoDTO.setNormalCourseFlag(1);
                userDoneActionInfoDTO.setBelongDate(queryInnoWeeklyInfoByIdListVO.getWorkBeginDatetime() != null ? queryInnoWeeklyInfoByIdListVO.getWorkBeginDatetime().toLocalDate() : null);
                biDataComponent.addUserDoneAction(userDoneActionInfoDTO);
            } catch (Exception e) {
                log.error("周作业同步完成动作信息异常：{}", e);
            }
        }
        return content.getId();
    }

    private void linkTopic(ContentPublishDTO dto, CommonContent content) {
        if (CollUtil.isNotEmpty(dto.getTopicIdList())) {
            commonTopicRelService.linkTopic(content.getId(), dto.getTopicIdList());
        }
        if (CollUtil.isNotEmpty(dto.getTopicList())) {
            // 如果有自定义话题，那么先新增自定义话题
            List<Long> existTopicTitleIdList = dto.getTopicList().stream().map(ContentPublishTopicDTO::getId).filter(Objects::nonNull).filter(i -> !Objects.equals(i, 0L)).collect(Collectors.toList());
            List<String> topicTitleList = dto.getTopicList().stream().filter(i -> Objects.isNull(i.getId()) || Objects.equals(i.getId(), 0L) && StrUtil.isNotBlank(i.getTitle())).map(ContentPublishTopicDTO::getTitle).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(topicTitleList)) {
                List<Long> topicIdList = commonTopicService.saveCustomTopic(topicTitleList);
                if (CollUtil.isNotEmpty(topicIdList)) {
                    existTopicTitleIdList.addAll(topicIdList);
                }
            }
            commonTopicRelService.linkTopic(content.getId(), existTopicTitleIdList);
        }
    }

    private void checkPic(ContentPublishDTO dto) {
        List<CompletableFuture<String>> cfList = dto.getContentList().stream()
                .filter(i -> CommonContentEnum.ContentBOType.IMAGE.getCode().equals(i.getType()))
                .filter(i -> StrUtil.isNotBlank(i.getContent()))
                .flatMap(i -> Stream.of(i.getContent().split(","))
                        .map(url -> CompletableFuture.supplyAsync(() -> {
                            String ossBucketName = null;
                            String ossObjectName = url.substring("https://resource.sibuqu.com/".length());
                            if (url.startsWith("https://resource.sibuqu.com")) {
                                ossBucketName = "diantai2019";
                                ossObjectName = url.substring("https://resource.sibuqu.com/".length());
                            } else if (url.startsWith("https://file.sibuqu.com/")) {
                                ossBucketName = "files-web";
                                ossObjectName = url.substring("https://file.sibuqu.com/".length());
                            }
                            boolean checkPic = checkManage.checkPic(url, ossBucketName, ossObjectName);
                            if (!checkPic) {
                                return "敏感图片，请修改后再发布";
                            }
                            return null;
                        }, quicklyExecutor).exceptionally(e -> {
                            log.error("图片审核报错：{}", e.getMessage(), e);
                            return null;
                        }))).collect(Collectors.toList());
        for (CompletableFuture<String> cf : cfList) {
            String s = cf.join();
            if (StrUtil.isNotBlank(s)) {
                throw new BusinessException(s);
            }
        }
    }

    public String getAllText(ContentPublishDTO dto) {
        String allText = "";
        if (StrUtil.isNotBlank(dto.getTitle())) {
            allText = dto.getTitle();
        }
        String text = dto.getContentList().stream().filter(i -> CommonContentEnum.ContentBOType.TEXT.getCode().equals(i.getType()))
                .map(ContentBO::getContent).collect(Collectors.joining(" "));
        if (StrUtil.isNotBlank(text)) {
            allText = allText + " " + text;
        }
        return allText;
    }

    private void setUserNameForClass(ContentPublishDTO dto, HeaderUserInfo headerUserInfo, CommonContent content) {
        if (Objects.equals(dto.getDataType(), 1)) {
            UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getDataId().intValue(), headerUserInfo.getId());
            if (userRightsCourse != null && userRightsCourse.getCompanyId() > 0) {
                String userName = userComponent.queryUserCompanyNameById(userRightsCourse.getCompanyId(), headerUserInfo.getId());
                content.setUserName(userName);
            } else {
                content.setUserName(headerUserInfo.getUserFullName());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long contentEdit(ContentEditDTO dto) {
        CommonContent content = getById(dto.getId());
        if (Objects.isNull(content)) {
            throw new BusinessException("内容不存在");
        }
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        if (!content.getUserId().equals(headerUserInfo.getId())) {
            throw new BusinessException("只能删除自己的内容");
        }
        if (CollUtil.isEmpty(dto.getContentList())) {
            throw new BusinessException("内容列表不能为空");
        }
        dto.getContentList().forEach(contentBO -> {
            if (StrUtil.isBlank(contentBO.getContent())) {
                throw new BusinessException("内容不能为空");
            }
        });
        // 润泽园敏感词
        checkContentLegal(dto.getContentList());
        BeanUtil.copyProperties(dto, content);
        content.setContent(getContentJson(dto.getContentList()));
        content.setContentType(getContentType(dto.getContentList()));
        content.setPicFlag(getPicFlag(dto.getContentList()));
        LocalDateTime now = LocalDateTime.now();
        FieldUtil.setUpdate(content, headerUserInfo, now);
        if (dto.getBeginTime() != null && dto.getBeginTime().isAfter(now)) {
            content.setShowStatus(0);
        }
        updateById(content);
        // 解除关联话题
        commonTopicRelService.unlinkTopic(content.getId());
        // 关联话题
        linkTopic(dto, content);
        // 校验文字
        CompletableFuture.runAsync(() -> checkContent(dto), quicklyExecutor).exceptionally((e) -> {
            log.error("contentPublish checkContent error:{}", e.getMessage(), e);
            content.setCheckStatus(CommonContentEnum.CheckStatus.NOT_PASS.getCode());
            content.setUpdateTime(LocalDateTime.now());
            updateById(content);
            return null;
        });
        // 校验图片
        CompletableFuture.runAsync(() -> checkPic(dto), quicklyExecutor).exceptionally((e) -> {
            log.error("contentPublish checkPic error:{}", e.getMessage(), e);
            content.setCheckStatus(CommonContentEnum.CheckStatus.NOT_PASS.getCode());
            content.setUpdateTime(LocalDateTime.now());
            updateById(content);
            return null;
        });
        return content.getId();
    }

    private void checkContent(ContentPublishDTO dto) {
        if (CollUtil.isEmpty(dto.getContentList())) {
            throw new BusinessException("内容列表不能为空");
        }
        dto.getContentList().forEach(contentBO -> {
            if (StrUtil.isBlank(contentBO.getContent())) {
                throw new BusinessException("内容不能为空");
            }
        });
        // 润泽园敏感词
        checkContentLegal(dto.getContentList());
        // 阿里云敏感词
        String allText = getAllText(dto);
        if (StrUtil.isNotBlank(allText)) {
            // 将字符串按照 600的长度切割成一个数组
            String[] textList = StrUtil.split(allText, 600);
            for (String text : textList) {
                boolean checkText = checkManage.checkText(text);
                if (!checkText) {
                    throw new BusinessException("涉及到敏感词，请修改后发布");
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long contentDel(IdLongDTO dto) {
        CommonContent content = getById(dto.getId());
        if (Objects.isNull(content)) {
            log.info("内容不存在");
            return 0L;
        }
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        if (!content.getUserId().equals(headerUserInfo.getId())) {
            throw new BusinessException("只能删除自己的内容");
        }
        log.info("删除内容headerUserInfo:{}, dto:{}", JSON.toJSONString(headerUserInfo), JSON.toJSONString(dto));
        removeById(dto.getId());
        commonTopicRelService.unlinkTopic(dto.getId());
        return 1L;
    }

    @Override
    public Long contentTop(ContentTopDTO dto) {
        CommonContent content = getById(dto.getId());
        if (Objects.isNull(content)) {
            throw new BusinessException("内容不存在");
        }
        if (CommonContentEnum.TopFlag.YES.getCode().equals(dto.getTopFlag())) {
            int count = count(new LambdaQueryWrapper<CommonContent>()
                    .eq(CommonContent::getDataType, content.getDataType())
                    .eq(CommonContent::getDataId, content.getDataId())
                    .eq(CommonContent::getSubDataId, content.getSubDataId())
                    .eq(CommonContent::getTopFlag, CommonContentEnum.TopFlag.YES.getCode())
                    .eq(CommonContent::getShowStatus, CommonContentEnum.ShowStatus.YES.getCode())
                    .eq(CommonContent::getDeleteFlag, IsDeleteEnum.NO.getCode()));
            if (count >= 5) {
                throw new BusinessException("置顶内容最多只能有5个");
            }
        }
        LocalDateTime now = LocalDateTime.now();
        content.setTopFlag(dto.getTopFlag());
        content.setTopTime(now);
        FieldUtil.setUpdate(content, HeaderUserInfoUtil.get(), now);
        updateById(content);
        return dto.getId();
    }

    @Override
    public CheckUserCanPublishVO checkUserCanPublishTopic(CheckUserCanPublishDTO dto) {
        CommonTopic commonTopic = commonTopicService.getById(dto.getTopicId());
        if (Objects.isNull(commonTopic)) {
            throw new BusinessException("话题不存在");
        }
        if (!commonTopic.getDataType().equals(1)) {
            throw new BusinessException("话题不存在");
        }
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        Integer courseId = commonTopic.getDataId().intValue();
        CourseGoodsDetailVO courseGoodsDetailVO = courseComponent.courseGoodsDetail(-1, courseId);
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(courseId, headerUserInfo.getId());
        if (Objects.isNull(userRightsCourse) || userRightsCourse.getId() == null) {
            throw new BusinessException("仅" + courseGoodsDetailVO.getCourseTitle() + "课程学员可以参与话题讨论");
        }
        CheckUserCanPublishVO checkUserCanPublishVO = new CheckUserCanPublishVO();
        checkUserCanPublishVO.setCourseId(courseId);
        checkUserCanPublishVO.setCourseName(courseGoodsDetailVO.getCourseTitle());
        ClassGroupInfoVO classesInfo = userComponent.getClassesInfo(headerUserInfo.getId(), courseId);
        if (!Objects.isNull(classesInfo)) {
            checkUserCanPublishVO.setClassGroupId(classesInfo.getClassGroupId());
            checkUserCanPublishVO.setClassName(classesInfo.getClassName());
        }
        checkUserCanPublishVO.setTopicId(dto.getTopicId());
        return checkUserCanPublishVO;
    }

    @Override
    public SquareContentListVO squareContentList(SquareContentListDTO dto) {
        if (Objects.equals(dto.getCursorId(), 0L)) dto.setCursorId(null);
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        List<CommonContent> list = null;
        if (Objects.equals(dto.getApiType(), 1)) {
            CommonContent cursorCommonContent = getCursorCommonContent(dto);
            LambdaQueryWrapper<CommonContent> queryWrapper = new LambdaQueryWrapper<>();
            commonQueryWrapper(queryWrapper);
            // 拼接游标
            cursorWrapper(queryWrapper, cursorCommonContent);
            list = list(queryWrapper);
        } else if (Objects.equals(dto.getApiType(), 2)) {
            List<Integer> userIdList = userComponent.getFollowUserIds(headerUserInfo.getId());
            List<Long> topicIdList = userFollowTopicService.userFollowTopicIds(headerUserInfo.getId());
            if (CollUtil.isEmpty(userIdList) && CollUtil.isEmpty(topicIdList)) {
                list = new ArrayList<>();
            } else {
                CommonContent cursorCommonContent = getCursorCommonContent(dto);
                list = baseMapper.followContentList(userIdList, topicIdList, cursorCommonContent);
            }
        } else if (Objects.equals(dto.getApiType(), 3)) {
            list = squareContentSearchList(dto, headerUserInfo);
        } else if (Objects.equals(dto.getApiType(), 4)) {
            CommonContent cursorCommonContent = getCursorCommonContent(dto);
            list = baseMapper.squareRecommendListByTopicId(dto, cursorCommonContent);
        } else if (Objects.equals(dto.getApiType(), 5)) {
            list = baseMapper.squareNewListByTopicId(dto);
        } else if (Objects.equals(dto.getApiType(), 6)) {
            list = list(new LambdaQueryWrapper<CommonContent>()
                    .lt(Objects.nonNull(dto.getCursorId()), CommonContent::getId, dto.getCursorId())
                    .eq(Objects.isNull(dto.getVisitUserId()), CommonContent::getUserId, headerUserInfo.getId())
                    .eq(Objects.nonNull(dto.getVisitUserId()), CommonContent::getUserId, dto.getVisitUserId())
                    .eq(CommonContent::getDataType, CommonContentEnum.DataType.SQUARE.getCode())
                    .eq(CommonContent::getShowStatus, CommonContentEnum.ShowStatus.YES.getCode())
                    .eq(CommonContent::getCheckStatus, CommonContentEnum.CheckStatus.PASS.getCode())
                    .eq(CommonContent::getDeleteFlag, IsDeleteEnum.NO.getCode())
                    .orderByDesc(CommonContent::getId)
                    .last("limit 20"));
        } else if (Objects.equals(dto.getApiType(), 7)) {
            list = baseMapper.videoContentList(dto);
        }
        SquareContentListVO squareContentListVO = contentListToSquareListVO(dto.getApiType(), list, headerUserInfo);
        // 如果是第一页，需要把自己当前一分钟内发布的内容查询出来拼到第一页
        querySquareLastMinutePublishContent(dto, headerUserInfo, squareContentListVO);
        return squareContentListVO;
    }

    private void cursorWrapper(LambdaQueryWrapper<CommonContent> queryWrapper, CommonContent cursorCommonContent) {
        queryWrapper.and(Objects.nonNull(cursorCommonContent), wrapper ->
                wrapper.lt(CommonContent::getRecommendValue, cursorCommonContent.getRecommendValue())
                        .or(orWrapper ->
                                orWrapper.eq(CommonContent::getRecommendValue, cursorCommonContent.getRecommendValue())
                                        .lt(CommonContent::getId, cursorCommonContent.getId())));
    }

    private void commonQueryWrapper(LambdaQueryWrapper<CommonContent> queryWrapper) {
        queryWrapper.eq(CommonContent::getCheckStatus, CommonContentEnum.CheckStatus.PASS.getCode())
                .eq(CommonContent::getDataType, CommonContentEnum.DataType.SQUARE.getCode())
                .eq(CommonContent::getShowStatus, CommonContentEnum.ShowStatus.YES.getCode())
                .eq(CommonContent::getDeleteFlag, IsDeleteEnum.NO.getCode())
                .orderByDesc(CommonContent::getRecommendValue)
                .orderByDesc(CommonContent::getId)
                .last("limit 20");
    }

    private CommonContent getCursorCommonContent(SquareContentListDTO dto) {
        CommonContent cursorCommonContent = null;
        if (Objects.nonNull(dto.getCursorId())) {
            cursorCommonContent = getById(dto.getCursorId());
        }
        return cursorCommonContent;
    }

    private List<CommonContent> squareContentSearchList(SquareContentListDTO dto, HeaderUserInfo headerUserInfo) {
        CommonContent cursorCommonContent = getCursorCommonContent(dto);
        LambdaQueryWrapper<CommonContent> queryWrapper = new LambdaQueryWrapper<>();
        commonQueryWrapper(queryWrapper);
        queryWrapper.and(StrUtil.isNotBlank(dto.getSearchContent()), wrapper ->
                wrapper.like(CommonContent::getContent, dto.getSearchContent())
                        .or()
                        .like(CommonContent::getTitle, dto.getSearchContent()));
        cursorWrapper(queryWrapper, cursorCommonContent);
        List<CommonContent> list = list(queryWrapper);
        // 保存搜索历史记录
        if (StrUtil.isNotBlank(dto.getSearchContent())) {
            CommonSearchHistory searchHistory = commonSearchHistoryService.getOneHistoryBySelf(2, headerUserInfo.getId(), dto.getSearchContent());
            if (Objects.nonNull(searchHistory)) {
                commonSearchHistoryService.removeById(searchHistory.getId());
            }
            commonSearchHistoryService.saveOneHistoryBySelf(2, headerUserInfo.getId(), dto.getSearchContent());
        }
        return list;
    }

    private void querySquareLastMinutePublishContent(SquareContentListDTO dto, HeaderUserInfo headerUserInfo, SquareContentListVO squareContentListVO) {
        if (Objects.equals(dto.getApiType(), 1) && Objects.isNull(dto.getCursorId())) {
            List<CommonContent> curUserList = list(new LambdaQueryWrapper<CommonContent>()
                    .eq(CommonContent::getUserId, headerUserInfo.getId())
                    .ge(CommonContent::getCreateTime, LocalDateTime.now().minusMinutes(1))
                    .eq(CommonContent::getDeleteFlag, IsDeleteEnum.NO.getCode())
                    .orderByDesc(CommonContent::getId)
            );
            List<SquareContentItemVO> voList = transferSquareList(curUserList, headerUserInfo);
            if (CollUtil.isNotEmpty(voList)) {
                // 先把 id 重复的移除
                squareContentListVO.getContentList().removeIf(i -> voList.stream().anyMatch(j -> Objects.equals(i.getId(), j.getId())));
                squareContentListVO.getContentList().addAll(0, voList);
            }
        }
    }

    @Override
    public List<SquareSearchHistoryItemVO> squareSearchHistoryList() {
        List<CommonSearchHistory> list = commonSearchHistoryService.listBySelf(2, HeaderUserInfoUtil.get().getId());
        return list.stream().map(i -> {
            SquareSearchHistoryItemVO item = new SquareSearchHistoryItemVO();
            item.setId(i.getId());
            item.setContent(i.getContent());
            return item;
        }).collect(Collectors.toList());
    }

    @Override
    public Integer tipOffContent(TipOffContentDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        CommonContent commonContent = getById(dto.getDataId());
        if (Objects.isNull(commonContent)) {
            throw new BusinessException("被举报的内容不存在");
        }
        CommonTipOff commonTipOff = BeanUtil.copyProperties(dto, CommonTipOff.class);
        commonTipOff.setTipOffUserId(headerUserInfo.getId());
        commonTipOff.setTipOffUserName(headerUserInfo.getUserFullName());
        commonTipOff.setBeTipOffUserId(commonContent.getUserId());
        commonTipOff.setBeTipOffUserName(commonContent.getUserName());
        FieldUtil.setCreateAndUpdate(commonTipOff, headerUserInfo, LocalDateTime.now());
        commonTipOffService.save(commonTipOff);
        return 1;
    }

    @Override
    public Integer squareDelSearchHistory(SquareDelSearchHistoryDTO dto) {
        if (CollUtil.isEmpty(dto.getIdList())) {
            return 1;
        }
        commonSearchHistoryService.removeByIds(dto.getIdList());
        return 1;
    }

    @Override
    public Long manageContentPublish(ManageContentPublishDTO dto) {
        UserInfoVO userInfoVO = userComponent.userById(dto.getPublishUserId());
        HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setId(userInfoVO.getId());
        headerUserInfo.setUserFullName(userInfoVO.getUserFullName());
        headerUserInfo.setPhone(userInfoVO.getPhone());
        HeaderUserInfoContext.set(headerUserInfo);
        ContentPublishDTO contentPublishDTO = new ContentPublishDTO();
        contentPublishDTO.setDataType(2);
        contentPublishDTO.setTitle(dto.getTitle());
        List<ContentBO> contentList = new ArrayList<>();
        ContentBO textBO = new ContentBO();
        textBO.setType(1);
        textBO.setContent(dto.getContent());
        contentList.add(textBO);
        if (Objects.equals(dto.getType(), 2)) {
            ContentBO picBO = new ContentBO();
            picBO.setType(2);
            picBO.setContent(String.join(",", dto.getMediaList()));
            contentList.add(picBO);
        }
        if (Objects.equals(dto.getType(), 3)) {
            for (String s : dto.getMediaList()) {
                ContentBO picBO = new ContentBO();
                picBO.setType(3);
                picBO.setContent(s);
                picBO.setCover(dto.getCover());
                contentList.add(picBO);
            }
        }
        contentPublishDTO.setContentList(contentList);
        contentPublishDTO.setTopicIdList(dto.getTopicIdList());
        contentPublishDTO.setBeginTime(dto.getBeginTime());
        return contentPublish(contentPublishDTO);
    }

    @Override
    public Integer calculateContentRecommendValue(List<CalculateContentRecommendValueDTO> list) {
        baseMapper.calculateContentRecommendValue(list);
        return 1;
    }

    private SquareContentListVO contentListToSquareListVO(Integer apiType, List<CommonContent> list, HeaderUserInfo headerUserInfo) {
        SquareContentListVO vo = new SquareContentListVO();
        if (CollUtil.isEmpty(list)) {
            return vo;
        }
        vo.setCursorId(list.get(list.size() - 1).getId());
        List<SquareContentItemVO> voList = transferSquareList(list, headerUserInfo);
        if (!Objects.equals(apiType, 2) && !Objects.equals(apiType, 5) && !Objects.equals(apiType, 6)) {
            Collections.shuffle(voList);
        }
        if (Objects.equals(apiType, 7)) {
            // 添加关注信息
            List<Integer> userIdList = voList.stream().map(SquareContentItemVO::getUserId).collect(Collectors.toList());
            Map<Integer, Integer> followMap = userComponent.batchGetUserFollow(headerUserInfo.getId(), userIdList);
            voList.forEach(i -> i.setFollowType(followMap.get(i.getUserId())));
        }
        vo.setContentList(voList);
        return vo;
    }

    private List<SquareContentItemVO> transferSquareList(List<CommonContent> list, HeaderUserInfo headerUserInfo) {
        List<Integer> contentIdList = list.stream().map(CommonContent::getId).map(Long::intValue).collect(Collectors.toList());
        List<Integer> userIdList = list.stream().map(CommonContent::getUserId).collect(Collectors.toList());
        Set<Integer> specialUserFlagSet = courseComponent.checkCourseSpecialUserList(7, userIdList, null);
        // 点赞评论数据
        Map<Integer, SearchCommentAndLikeTotalVO> likeTotalVOMap =
                searchCommentAndLikeTotalVOMap(CommentTypeEnum.COMMON_CONTENT.getCode(), LikeTypeEnum.COMMON_CONTENT.getCode(),
                        contentIdList, headerUserInfo);
        Map<Integer, String> userFullNameMap = userComponent.userFullNameByIdList(list.stream().map(CommonContent::getUserId).collect(Collectors.toList()));
        // topic 列表
        Map<Long, List<CommonTopicVO>> topicRelByDataIdMap = commonTopicRelService.getTopicRelByDataId(contentIdList);
        return list.stream().map(i -> {
            SquareContentItemVO itemVO = BeanUtil.copyProperties(i, SquareContentItemVO.class);
            itemVO.setContentList(genContentList(i.getContent()));
            String userFullName = userFullNameMap.get(i.getUserId());
            if (Objects.nonNull(userFullName)) {
                itemVO.setUserName(userFullName);
            }
            SearchCommentAndLikeTotalVO searchCommentAndLikeTotalVO = likeTotalVOMap.get(i.getId().intValue());
            if (Objects.nonNull(searchCommentAndLikeTotalVO)) {
                itemVO.setCommentTotal(searchCommentAndLikeTotalVO.getCommentTotal());
                itemVO.setLikeTotal(searchCommentAndLikeTotalVO.getLikeTotal());
                itemVO.setLikeFlag(searchCommentAndLikeTotalVO.getLikeFlag());
            } else {
                itemVO.setCommentTotal(0);
                itemVO.setLikeTotal(0);
                itemVO.setLikeFlag(0);
            }
            itemVO.setTopicVOS(topicRelByDataIdMap.get(i.getId()));
            itemVO.setSpecialUserFlag(specialUserFlagSet.contains(i.getUserId()) ? 1 : 0);
            return itemVO;
        }).collect(Collectors.toList());
    }

    private void checkContentLegal(List<ContentBO> contentList) {
        for (ContentBO contentBO : contentList) {
            if (CommonContentEnum.ContentBOType.TEXT.getCode().equals(contentBO.getType())) {
                SensitiveWordsInfoVO sensitiveWordsInfoVO = sensitiveWordsService.containFirstSensitiveWordsInfo(contentBO.getContent(), SensitiveWordsTypeEnum.LIVE.getCode(), null);
                if (Objects.nonNull(sensitiveWordsInfoVO) && sensitiveWordsInfoVO.getIsContainSensitiveWords()) {
                    throw new BusinessException("存在敏感词[" + sensitiveWordsInfoVO.getSensitiveWord() + "]");
                }
            }
        }

    }

    @Override
    public List<ContentBO> genContentList(String content) {
        if (StrUtil.isBlank(content)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(content, ContentBO.class);
    }

    @Override
    public String getContentJson(List<ContentBO> contentList) {
        if (CollUtil.isEmpty(contentList)) {
            return "[]";
        }
        return JSON.toJSONString(contentList, SerializerFeature.SkipTransientField);
    }

    @Override
    public Integer getContentType(List<ContentBO> contentList) {
        if (CollUtil.isEmpty(contentList)) {
            return CommonContentEnum.ContentType.IMAGE.getCode();
        }
        for (ContentBO contentBO : contentList) {
            if (CommonContentEnum.ContentBOType.AUDIO.getCode().equals(contentBO.getType())) {
                return CommonContentEnum.ContentType.AUDIO.getCode();
            } else if (CommonContentEnum.ContentBOType.VIDEO.getCode().equals(contentBO.getType())) {
                return CommonContentEnum.ContentType.VIDEO.getCode();
            }
        }
        return CommonContentEnum.ContentType.IMAGE.getCode();
    }

    @Override
    public Integer getPicFlag(List<ContentBO> contentList) {
        if (CollUtil.isEmpty(contentList)) {
            return 0;
        }
        for (ContentBO contentBO : contentList) {
            if (CommonContentEnum.ContentBOType.IMAGE.getCode().equals(contentBO.getType())) {
                return 1;
            }
        }
        return 0;
    }

    @Override
    public List<GetContentListByDataIdSubDataIdVO> getContentListByDataIdSubDataId(GetContentListByDataIdSubDataIdDTO dto) {
        List<CommonContent> list = list(new LambdaQueryWrapper<CommonContent>()
                .eq(CommonContent::getDataType, dto.getDataType())
                .eq(CommonContent::getDataId, dto.getDataId())
                .eq(Objects.nonNull(dto.getSubDataId()), CommonContent::getSubDataId, dto.getSubDataId())
                .eq(CommonContent::getUserId, dto.getUserId())
                .eq(CommonContent::getDeleteFlag, IsDeleteEnum.NO.getCode())
                .orderByDesc(CommonContent::getId));
        return list.stream().map(i -> {
            GetContentListByDataIdSubDataIdVO vo = new GetContentListByDataIdSubDataIdVO();
            BeanUtil.copyProperties(i, vo);
            vo.setContentList(genContentList(i.getContent()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public PageInfoBT<WeekWorkPageListVO> weekWorkPageList(WeekWorkPageListDTO dto) {
        Integer userIdByUserName = null;
        if (StrUtil.isNotBlank(dto.getName())) {
            userIdByUserName = baseUserComponent.innoSelectUserIdByName(dto.getName());
        }
        Integer userIdByPhone = null;
        if (StrUtil.isNotBlank(dto.getPhone())) {
            UserInfoVO userInfoVO = userComponent.selectValidUserByPhone(dto.getPhone());
            if (Objects.nonNull(userInfoVO)) {
                userIdByPhone = userInfoVO.getId();
            }
        }
        IPage<CommonContent> page = page(new Page<>(dto.getPageNum(), dto.getPageSize()),
                new LambdaQueryWrapper<CommonContent>()
                        .eq(CommonContent::getDataType, CommonContentEnum.DataType.INNO_WEEK_WORK.getCode())
                        .eq(CommonContent::getDataId, dto.getCourseId())
                        .eq(Objects.nonNull(dto.getRecommendFlag()), CommonContent::getRecommendFlag, dto.getRecommendFlag())
                        .eq(Objects.nonNull(dto.getWeekWorkId()), CommonContent::getSubDataId, dto.getWeekWorkId())
                        .eq(Objects.nonNull(userIdByUserName), CommonContent::getUserId, userIdByUserName)
                        .eq(Objects.nonNull(userIdByPhone), CommonContent::getUserId, userIdByPhone)
                        .eq(Objects.nonNull(dto.getPicFlag()), CommonContent::getPicFlag, dto.getPicFlag())
                        .eq(CommonContent::getDeleteFlag, IsDeleteEnum.NO.getCode())
                        .orderByDesc(CommonContent::getId));
        if (CollUtil.isEmpty(page.getRecords())) {
            return PageInfoBT.noData();
        }
        List<Integer> userIdList = page.getRecords().stream().map(CommonContent::getUserId).collect(Collectors.toList());
        Map<Integer, String> userFullNameMap = baseUserComponent.innoUserFullNameByUserIdList(userIdList);
        Map<Integer, String> userPhoneMap = baseUserComponent.userPhoneByUserIdList(userIdList);
        // 作业标题列表
        List<Long> weekWorkIdList = page.getRecords().stream().map(CommonContent::getSubDataId).collect(Collectors.toList());
        Map<Long, String> weekWorkTitleMap = workComponent.queryInnoWeeklyInfoByIdList(weekWorkIdList).entrySet()
                .stream().collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getTitle()));
        IPage<WeekWorkPageListVO> voPage = page.convert(i -> {
            WeekWorkPageListVO vo = new WeekWorkPageListVO();
            BeanUtil.copyProperties(i, vo);
            vo.setContentList(genContentList(i.getContent()));
            vo.setUserName(userFullNameMap.get(i.getUserId()));
            vo.setUserPhone(userPhoneMap.get(i.getUserId()));
            vo.setWeekWorkTitle(weekWorkTitleMap.get(i.getSubDataId()));
            return vo;
        });
        return PageInfoBT.fromPage(voPage);
    }

    @Override
    public Integer recommendContent(RecommendContentDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        CommonContent commonContent = getById(dto.getId());
        if (Objects.isNull(commonContent)) {
            return 0;
        }
        commonContent.setRecommendFlag(dto.getRecommendFlag());
        FieldUtil.setUpdate(commonContent, headerUserInfo, LocalDateTime.now());
        updateById(commonContent);
        return 1;
    }

    @Override
    public Integer hideContent(HideContentDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        CommonContent commonContent = getById(dto.getId());
        if (Objects.isNull(commonContent)) {
            return 0;
        }
        commonContent.setDataFlag(dto.getDataFlag());
        FieldUtil.setUpdate(commonContent, headerUserInfo, LocalDateTime.now());
        updateById(commonContent);
        return 1;
    }

    @Override
    public PageInfoBT<WeekWorkItemVO> weekWorkList(WeekWorkListDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        LambdaQueryWrapper<CommonContent> wrapper = new LambdaQueryWrapper<CommonContent>();
        if (Objects.equals(dto.getMyWork(), 1)) {
            dto.setType(2);
            wrapper.eq(CommonContent::getUserId, headerUserInfo.getId());
        }
        if (Objects.nonNull(dto.getTribeId()) && Objects.equals(dto.getTribeHomeFlag(), 0)) {
            List<Integer> tribeUserIds = classesComponent.tribeUserIds(dto.getTribeId());
            if (CollUtil.isNotEmpty(tribeUserIds)) {
                wrapper.in(CommonContent::getUserId, tribeUserIds);
            }
        }
        wrapper.eq(CommonContent::getDataType, CommonContentEnum.DataType.INNO_WEEK_WORK.getCode())
                .eq(CommonContent::getDataId, dto.getCourseId())
                .eq(Objects.equals(dto.getTribeHomeFlag(), 1), CommonContent::getRecommendFlag, 1)
                .eq(CommonContent::getDeleteFlag, IsDeleteEnum.NO.getCode());
        if (Objects.equals(dto.getType(), 2)) {
            wrapper.orderByDesc(CommonContent::getId);
        } else {
            wrapper.eq(CommonContent::getRecommendFlag, 1);
            wrapper.orderByDesc(CommonContent::getRecommendValue)
                    .orderByAsc(CommonContent::getId);
        }
        Page<CommonContent> page = page(new Page<>(dto.getPageNum(), dto.getPageSize()), wrapper);
        if (CollUtil.isEmpty(page.getRecords())) {
            return PageInfoBT.noData();
        }
        List<Integer> idList = page.getRecords().stream().map(i -> i.getId().intValue()).collect(Collectors.toList());
        // 点赞评论数据
        Map<Integer, SearchCommentAndLikeTotalVO> likeTotalVOMap =
                searchCommentAndLikeTotalVOMap(CommentTypeEnum.COMMON_CONTENT.getCode(), LikeTypeEnum.COMMON_CONTENT.getCode(),
                        idList, headerUserInfo);
        IPage<WeekWorkItemVO> voPage = page.convert(i -> {
            WeekWorkItemVO vo = new WeekWorkItemVO();
            BeanUtil.copyProperties(i, vo);
            vo.setContentList(genContentList(i.getContent()));
            vo.setCreateTimeStr(CommonUtils.getCreateTimeStr(i.getCreateTime(), null));
            SearchCommentAndLikeTotalVO searchCommentAndLikeTotalVO = likeTotalVOMap.get(i.getId().intValue());
            if (Objects.nonNull(searchCommentAndLikeTotalVO)) {
                vo.setCommentTotal(searchCommentAndLikeTotalVO.getCommentTotal());
                vo.setLikeTotal(searchCommentAndLikeTotalVO.getLikeTotal());
                vo.setLikeFlag(searchCommentAndLikeTotalVO.getLikeFlag());
            } else {
                vo.setCommentTotal(0);
                vo.setLikeTotal(0);
                vo.setLikeFlag(0);
            }
            return vo;
        });
        return PageInfoBT.fromPage(voPage);
    }

    @Override
    public List<CheckUserSubmitWeekWorkVO> checkUserSubmitWeekWork(CheckUserSubmitWeekWorkDTO dto) {
        if (CollUtil.isEmpty(dto.getWeekWorkIdList()) || CollUtil.isEmpty(dto.getUserIdList())) {
            return new ArrayList<>();
        }
        return list(new LambdaQueryWrapper<CommonContent>()
                .eq(CommonContent::getDataType, CommonContentEnum.DataType.INNO_WEEK_WORK.getCode())
                .eq(CommonContent::getDataId, dto.getCourseId())
                .in(CommonContent::getSubDataId, dto.getWeekWorkIdList())
                .in(CommonContent::getUserId, dto.getUserIdList())
                .eq(CommonContent::getDeleteFlag, IsDeleteEnum.NO.getCode()))
                .stream().map(i -> {
                    CheckUserSubmitWeekWorkVO vo = new CheckUserSubmitWeekWorkVO();
                    vo.setWeekWorkId(i.getSubDataId());
                    vo.setUserId(i.getUserId());
                    vo.setSubmitFlag(true);
                    return vo;
                }).collect(Collectors.toList());
    }

    @Override
    public PageInfoBT<WeekWorkItemVO> teamWorkList(TeamWorkListDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        LambdaQueryWrapper<CommonContent> wrapper = new LambdaQueryWrapper<CommonContent>();
        if (dto.getTeamId() != null && dto.getTeamId() != 0) {
            List<Integer> tribeUserIds = classesComponent.teamUserIds(dto.getTeamId());
            if (CollUtil.isNotEmpty(tribeUserIds)) {
                wrapper.in(CommonContent::getUserId, tribeUserIds);
            } else {
                return PageInfoBT.noData();
            }
        } else {
            return PageInfoBT.noData();
        }
        if (Objects.equals(dto.getType(), 2)) {
            wrapper.orderByDesc(CommonContent::getId);
        } else {
            wrapper.eq(CommonContent::getRecommendFlag, 1);
            wrapper.orderByDesc(CommonContent::getRecommendValue)
                    .orderByAsc(CommonContent::getId);
        }
        wrapper.eq(CommonContent::getDataType, CommonContentEnum.DataType.INNO_WEEK_WORK.getCode())
                .eq(CommonContent::getDataId, dto.getCourseId())
                .eq(CommonContent::getDeleteFlag, IsDeleteEnum.NO.getCode());

        Page<CommonContent> page = page(new Page<>(dto.getPageNum(), dto.getPageSize()), wrapper);
        if (CollUtil.isEmpty(page.getRecords())) {
            return PageInfoBT.noData();
        }
        List<Integer> idList = page.getRecords().stream().map(i -> i.getId().intValue()).collect(Collectors.toList());
        // 点赞评论数据
        Map<Integer, SearchCommentAndLikeTotalVO> likeTotalVOMap =
                searchCommentAndLikeTotalVOMap(CommentTypeEnum.COMMON_CONTENT.getCode(), LikeTypeEnum.COMMON_CONTENT.getCode(),
                        idList, headerUserInfo);
        IPage<WeekWorkItemVO> voPage = page.convert(i -> {
            WeekWorkItemVO vo = new WeekWorkItemVO();
            BeanUtil.copyProperties(i, vo);
            vo.setContentList(genContentList(i.getContent()));
            vo.setCreateTimeStr(CommonUtils.getCreateTimeStr(i.getCreateTime(), null));
            SearchCommentAndLikeTotalVO searchCommentAndLikeTotalVO = likeTotalVOMap.get(i.getId().intValue());
            if (Objects.nonNull(searchCommentAndLikeTotalVO)) {
                vo.setCommentTotal(searchCommentAndLikeTotalVO.getCommentTotal());
                vo.setLikeTotal(searchCommentAndLikeTotalVO.getLikeTotal());
                vo.setLikeFlag(searchCommentAndLikeTotalVO.getLikeFlag());
            } else {
                vo.setCommentTotal(0);
                vo.setLikeTotal(0);
                vo.setLikeFlag(0);
            }
            return vo;
        });
        return PageInfoBT.fromPage(voPage);
    }

    //获取点赞评论信息
    private Map<Integer, SearchCommentAndLikeTotalVO> searchCommentAndLikeTotalVOMap(Integer commentType,
                                                                                     Integer likeType,
                                                                                     List<Integer> dataIdList,
                                                                                     HeaderUserInfo headerUserInfo) {
        SearchCommentAndLikeTotalDTO searchCommentAndLikeTotalDTO = new SearchCommentAndLikeTotalDTO();
        searchCommentAndLikeTotalDTO.setCommentType(commentType);
        searchCommentAndLikeTotalDTO.setLikeType(likeType);
        searchCommentAndLikeTotalDTO.setDataIdList(dataIdList);
        searchCommentAndLikeTotalDTO.setUserId(headerUserInfo.getId());
        return commentRecordService.searchCommentAndLikeTotal(searchCommentAndLikeTotalDTO).stream().collect(
                Collectors.toMap(SearchCommentAndLikeTotalVO::getDataId, Function.identity()));
    }
}
