package com.sibuqu.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.common.bo.ContentBO;
import com.sibuqu.common.dto.IdLongDTO;
import com.sibuqu.common.dto.content.CalculateContentRecommendValueDTO;
import com.sibuqu.common.dto.content.CheckUserCanPublishDTO;
import com.sibuqu.common.dto.content.CheckUserSubmitWeekWorkDTO;
import com.sibuqu.common.dto.content.CommonContentInfoDTO;
import com.sibuqu.common.dto.content.CommonContentPageListDTO;
import com.sibuqu.common.dto.common.TopContentDTO;
import com.sibuqu.common.dto.content.ContentEditDTO;
import com.sibuqu.common.dto.content.ContentPublishDTO;
import com.sibuqu.common.dto.content.ContentTopDTO;
import com.sibuqu.common.dto.content.GetContentListByDataIdSubDataIdDTO;
import com.sibuqu.common.dto.content.HideContentDTO;
import com.sibuqu.common.dto.content.ManageContentPublishDTO;
import com.sibuqu.common.dto.content.RecommendContentDTO;
import com.sibuqu.common.dto.content.SquareContentListDTO;
import com.sibuqu.common.dto.content.SquareDelSearchHistoryDTO;
import com.sibuqu.common.dto.content.TeamWorkListDTO;
import com.sibuqu.common.dto.content.TipOffContentDTO;
import com.sibuqu.common.dto.content.WeekWorkPageListDTO;
import com.sibuqu.common.dto.content.WeekWorkListDTO;
import com.sibuqu.common.entity.CommonContent;
import com.sibuqu.common.service.bo.PageListBO;
import com.sibuqu.common.vo.content.CheckUserCanPublishVO;
import com.sibuqu.common.vo.content.CheckUserSubmitWeekWorkVO;
import com.sibuqu.common.vo.content.CommonContentInfoVO;
import com.sibuqu.common.vo.content.CommonContentPageListVO;
import com.sibuqu.common.vo.content.GetContentListByDataIdSubDataIdVO;
import com.sibuqu.common.vo.content.SquareContentListVO;
import com.sibuqu.common.vo.content.SquareSearchHistoryItemVO;
import com.sibuqu.common.vo.content.WeekWorkItemVO;
import com.sibuqu.common.vo.content.WeekWorkPageListVO;
import com.sibuqu.common.vo.content.WeekWorkListVO;

import java.util.List;

public interface CommonContentService extends IService<CommonContent> {

    PageListBO<CommonContentPageListVO> pageList(CommonContentPageListDTO dto);

    List<CommonContentPageListVO> topContentList(TopContentDTO dto);

    CommonContentInfoVO contentInfo(CommonContentInfoDTO dto);

    Long contentPublish(ContentPublishDTO dto);

    /**
     * 根据content转为内容列表
     */
    List<ContentBO> genContentList(String content);

    /**
     * 根据内容列表转为content
     */
    String getContentJson(List<ContentBO> contentList);

    /**
     * 根据内容列表获取内容类型
     */
    Integer getContentType(List<ContentBO> contentList);

    /**
     * 根据内容列表获取是否有图片
     */
    Integer getPicFlag(List<ContentBO> contentList);

    Long contentEdit(ContentEditDTO dto);

    Long contentDel(IdLongDTO dto);

    Long contentTop(ContentTopDTO dto);

    CheckUserCanPublishVO checkUserCanPublishTopic(CheckUserCanPublishDTO dto);

    SquareContentListVO squareContentList(SquareContentListDTO dto);

    List<SquareSearchHistoryItemVO> squareSearchHistoryList();

    Integer tipOffContent(TipOffContentDTO dto);

    Integer squareDelSearchHistory(SquareDelSearchHistoryDTO dto);

    Long manageContentPublish(ManageContentPublishDTO dto);

    Integer calculateContentRecommendValue(List<CalculateContentRecommendValueDTO> list);

    List<GetContentListByDataIdSubDataIdVO> getContentListByDataIdSubDataId(GetContentListByDataIdSubDataIdDTO dto);

    PageInfoBT<WeekWorkPageListVO> weekWorkPageList(WeekWorkPageListDTO dto);

    Integer recommendContent(RecommendContentDTO dto);

    Integer hideContent(HideContentDTO dto);

    PageInfoBT<WeekWorkItemVO> weekWorkList(WeekWorkListDTO dto);

    List<CheckUserSubmitWeekWorkVO> checkUserSubmitWeekWork(CheckUserSubmitWeekWorkDTO dto);

    PageInfoBT<WeekWorkItemVO> teamWorkList(TeamWorkListDTO dto);
}
