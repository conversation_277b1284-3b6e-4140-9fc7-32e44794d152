package com.sibuqu.common.controller.common;

import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.common.dto.IdLongDTO;
import com.sibuqu.common.dto.content.CalculateContentRecommendValueDTO;
import com.sibuqu.common.dto.content.CheckUserCanPublishDTO;
import com.sibuqu.common.dto.content.CheckUserSubmitWeekWorkDTO;
import com.sibuqu.common.dto.content.CommonContentInfoDTO;
import com.sibuqu.common.dto.content.CommonContentPageListDTO;
import com.sibuqu.common.dto.common.TopContentDTO;
import com.sibuqu.common.dto.content.ContentEditDTO;
import com.sibuqu.common.dto.content.ContentPublishDTO;
import com.sibuqu.common.dto.content.ContentTopDTO;
import com.sibuqu.common.dto.content.GetContentListByDataIdSubDataIdDTO;
import com.sibuqu.common.dto.content.HideContentDTO;
import com.sibuqu.common.dto.content.ManageContentPublishDTO;
import com.sibuqu.common.dto.content.RecommendContentDTO;
import com.sibuqu.common.dto.content.SquareContentListDTO;
import com.sibuqu.common.dto.content.SquareDelSearchHistoryDTO;
import com.sibuqu.common.dto.content.TeamWorkListDTO;
import com.sibuqu.common.dto.content.TipOffContentDTO;
import com.sibuqu.common.dto.content.WeekWorkPageListDTO;
import com.sibuqu.common.service.CommonContentService;
import com.sibuqu.common.service.bo.PageListBO;
import com.sibuqu.common.vo.content.CheckUserCanPublishVO;
import com.sibuqu.common.vo.content.CheckUserSubmitWeekWorkVO;
import com.sibuqu.common.vo.content.CommonContentInfoVO;
import com.sibuqu.common.vo.content.CommonContentPageListVO;
import com.sibuqu.common.vo.content.GetContentListByDataIdSubDataIdVO;
import com.sibuqu.common.vo.content.SquareContentListVO;
import com.sibuqu.common.vo.content.SquareSearchHistoryItemVO;
import com.sibuqu.common.vo.content.WeekWorkItemVO;
import com.sibuqu.common.vo.content.WeekWorkPageListVO;
import com.sibuqu.common.dto.content.WeekWorkListDTO;
import com.sibuqu.common.vo.content.WeekWorkListVO;
import com.sibuqu.starter.redis.aop.DistributedLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(value = "用户内容", tags = { "用户内容" })
@RestController
@RequestMapping("/appcommon/common_content")
public class CommonContentController {

    @Autowired
    private CommonContentService commonContentService;

    @ApiOperation("置顶内容列表")
    @PostMapping("/topContentList")
    public ResultInfo<List<CommonContentPageListVO>> topContentList(@RequestBody @Validated TopContentDTO dto) {
        return ResultInfo.ok(commonContentService.topContentList(dto));
    }

    @ApiOperation("内容分页列表")
    @PostMapping("/pageList")
    public ResultInfo<PageListBO<CommonContentPageListVO>> pageList(
            @RequestBody @Validated CommonContentPageListDTO dto) {
        return ResultInfo.ok(commonContentService.pageList(dto));
    }

    @ApiOperation("内容详情")
    @PostMapping("/contentInfo")
    public ResultInfo<CommonContentInfoVO> contentInfo(@RequestBody @Validated CommonContentInfoDTO dto) {
        return ResultInfo.ok(commonContentService.contentInfo(dto));
    }

    @ApiOperation("内容发布")
    @PostMapping("/contentPublish")
    @DistributedLock
    public ResultInfo<Long> contentPublish(@RequestBody @Validated ContentPublishDTO dto) {
        return ResultInfo.ok(commonContentService.contentPublish(dto));
    }

    @ApiOperation("管理后台-内容发布")
    @PostMapping("/manageContentPublish")
    public ResultInfo<Long> manageContentPublish(@RequestBody @Validated ManageContentPublishDTO dto) {
        return ResultInfo.ok(commonContentService.manageContentPublish(dto));
    }

    @ApiOperation("内容编辑")
    @PostMapping("/contentEdit")
    @DistributedLock
    public ResultInfo<Long> contentEdit(@RequestBody @Validated ContentEditDTO dto) {
        return ResultInfo.ok(commonContentService.contentEdit(dto));
    }

    @ApiOperation("内容删除")
    @PostMapping("/contentDel")
    @DistributedLock
    public ResultInfo<Long> contentDel(@RequestBody @Validated IdLongDTO dto) {
        return ResultInfo.ok(commonContentService.contentDel(dto));
    }

    @ApiOperation("内容置顶")
    @PostMapping("/contentTop")
    @DistributedLock
    public ResultInfo<Long> contentTop(@RequestBody @Validated ContentTopDTO dto) {
        return ResultInfo.ok(commonContentService.contentTop(dto));
    }

    @ApiOperation("校验用户是否可以发布话题")
    @PostMapping("/checkUserCanPublishTopic")
    public ResultInfo<CheckUserCanPublishVO> checkUserCanPublishTopic(
            @RequestBody @Validated CheckUserCanPublishDTO dto) {
        return ResultInfo.ok(commonContentService.checkUserCanPublishTopic(dto));
    }

    @ApiOperation("广场-内容列表")
    @PostMapping("/squareContentList")
    public ResultInfo<SquareContentListVO> squareContentList(@RequestBody @Validated SquareContentListDTO dto) {
        return ResultInfo.ok(commonContentService.squareContentList(dto));
    }

    @ApiOperation("广场-搜索记录查询")
    @PostMapping("/squareSearchHistoryList")
    public ResultInfo<List<SquareSearchHistoryItemVO>> squareSearchHistoryList() {
        return ResultInfo.ok(commonContentService.squareSearchHistoryList());
    }

    @ApiOperation("广场-批量删除搜索记录")
    @PostMapping("/squareDelSearchHistory")
    public ResultInfo<Integer> squareDelSearchHistory(@RequestBody @Validated SquareDelSearchHistoryDTO dto) {
        return ResultInfo.ok(commonContentService.squareDelSearchHistory(dto));
    }

    @ApiOperation("对内容的举报")
    @PostMapping("/tipOffContent")
    @DistributedLock
    public ResultInfo<Integer> tipOffContent(@RequestBody @Validated TipOffContentDTO dto) {
        return ResultInfo.ok(commonContentService.tipOffContent(dto));
    }

    @ApiOperation("内部调用-计算内容推荐值")
    @PostMapping("/calculateContentRecommendValue")
    public ResultInfo<Integer> calculateContentRecommendValue(
            @RequestBody @Validated List<CalculateContentRecommendValueDTO> list) {
        return ResultInfo.ok(commonContentService.calculateContentRecommendValue(list));
    }

    @ApiOperation("内部调用-根据 dataIdSubDataId 获取内容列表")
    @PostMapping("/getContentListByDataIdSubDataId")
    public ResultInfo<List<GetContentListByDataIdSubDataIdVO>> getContentListByDataIdSubDataId(
            @RequestBody @Validated GetContentListByDataIdSubDataIdDTO dto) {
        return ResultInfo.ok(commonContentService.getContentListByDataIdSubDataId(dto));
    }

    @ApiOperation("管理后台-周作业分页列表")
    @PostMapping("/weekWorkPageList")
    public ResultInfo<PageInfoBT<WeekWorkPageListVO>> weekWorkPageList(
            @RequestBody @Validated WeekWorkPageListDTO dto) {
        return ResultInfo.ok(commonContentService.weekWorkPageList(dto));
    }

    @ApiOperation("管理后台-推荐、取消推荐")
    @PostMapping("/recommendContent")
    public ResultInfo<Integer> recommendContent(@RequestBody @Validated RecommendContentDTO dto) {
        return ResultInfo.ok(commonContentService.recommendContent(dto));
    }

    @ApiOperation("管理后台-隐藏、取消隐藏")
    @PostMapping("/hideContent")
    public ResultInfo<Integer> hideContent(@RequestBody @Validated HideContentDTO dto) {
        return ResultInfo.ok(commonContentService.hideContent(dto));
    }

    @ApiOperation("周作业列表")
    @PostMapping("/weekWorkList")
    public ResultInfo<PageInfoBT<WeekWorkItemVO>> weekWorkList(@RequestBody @Validated WeekWorkListDTO dto) {
        return ResultInfo.ok(commonContentService.weekWorkList(dto));
    }

    @ApiOperation("根据周作业 id 列表用户 id列表查询是否提交")
    @PostMapping("/checkUserSubmitWeekWork")
    public ResultInfo<List<CheckUserSubmitWeekWorkVO>> checkUserSubmitWeekWork(@RequestBody @Validated CheckUserSubmitWeekWorkDTO dto) {
        return ResultInfo.ok(commonContentService.checkUserSubmitWeekWork(dto));
    }
    @ApiOperation("团队作业列表")
    @PostMapping("/teamWorkList")
    public ResultInfo<PageInfoBT<WeekWorkItemVO>> teamWorkList(@RequestBody @Validated TeamWorkListDTO dto) {
        return ResultInfo.ok(commonContentService.teamWorkList(dto));
    }
}
