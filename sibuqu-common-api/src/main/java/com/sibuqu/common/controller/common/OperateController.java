package com.sibuqu.common.controller.common;

import cn.hutool.core.util.ObjectUtil;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.common.annation.RequestHeaderUser;
import com.sibuqu.common.dto.common.*;
import com.sibuqu.common.service.common.*;
import com.sibuqu.common.vo.common.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <p>
 * 公共服务-运营相关接口 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@Api(value = "公共服务-运营相关接口", tags = {"公共服务-运营相关接口"})
@Slf4j
@RestController
@RequestMapping("/appcommon/operate")
public class OperateController {

    @Autowired
    CertificateInfoService certificateInfoService;
    @Autowired
    CourseShareService courseShareService;
    @Autowired
    CoursePosterMaximService coursePosterMaximService;
    @Autowired
    UserFeedbackService userFeedbackService;
    @Autowired
    UserFeedbackResourceService userFeedbackResourceService;
    @Autowired
    PendantConfigService pendantConfigService;
    @Autowired
    BannerConfigService bannerConfigService;
    @Autowired
    CommonPhoneCodeService commonPhoneCodeService;

    @ApiOperation(value = "Saas服务的企业banner列表", notes = "Saas服务的企业banner列表")
    @PostMapping("/saasCompanyBannerList")
    public ResultInfo<List<BannerConfigVO>> saasCompanyBannerList(@RequestBody SaasCompanyBannerSearchDTO dto) {
        return ResultInfo.ok(bannerConfigService.saasCompanyBannerList(dto));
    }

    @ApiOperation(value = "Saas服务的企业banner编辑", notes = "Saas服务的企业banner编辑")
    @PostMapping("/saasCompanyBannerEdit")
    public ResultInfo<Boolean> saasCompanyBannerEdit(@RequestBody SaasCompanyBannerEditInfoDTO dto) {
        return ResultInfo.ok(bannerConfigService.saasCompanyBannerEdit(dto));
    }

    /**
     * 根据手机号获取验证码
     * phone 手机号
     * type 1短信，2语音
     *
     * @return
     */
    @ApiOperation(value = "根据手机号获取验证码", notes = "根据手机号获取验证码")
    @PostMapping("/getPhoneCode")
    public ResultInfo<Boolean>  getPhoneCode(@RequestBody CommonPhoneCodeDTO commonPhoneCodeDTO) {
        return ResultInfo.ok(commonPhoneCodeService.getPhoneCode(commonPhoneCodeDTO));
    }

    @ApiOperation(value = "经典撞击首页", notes = "经典撞击首页")
    @PostMapping("/classicsHitHome")
    public ResultInfo<ClassicsHitHomeVO> classicsHitHome() {
        return ResultInfo.ok(coursePosterMaximService.classicsHitHome());
    }

    @ApiOperation(value = "h5随机取一条课程箴言", notes = "h5随机取一条课程箴言")
    @PostMapping("/randomPosterMaxim")
    public ResultInfo<CoursePosterMaximVO> randomPosterMaxim(@RequestBody @Validated CoursePosterMaximSearchDTO dto, BindingResult bindingResult,
                                                             @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        return ResultInfo.ok(coursePosterMaximService.randomPosterMaxim(dto, headerUserInfo));
    }

    @ApiOperation(value = "h5课程箴言海报列表", notes = "h5课程箴言海报列表")
    @PostMapping("/posterMaximPage")
    public ResultInfo<PageInfoBT<CoursePosterMaximVO>> posterMaximPage(@RequestBody @Validated CoursePosterMaximSearchDTO dto, BindingResult bindingResult) {
        return ResultInfo.ok(coursePosterMaximService.posterMaximPage(dto));
    }

    @ApiOperation(value = "pc随机取一条课程箴言", notes = "pc随机取一条课程箴言")
    @PostMapping("/pc/randomPosterMaxim")
    public ResultInfo<CoursePosterMaximVO> pcRandomPosterMaxim(@RequestBody @Validated CoursePosterMaximSearchDTO dto, BindingResult bindingResult) {
        return ResultInfo.ok(coursePosterMaximService.pcRandomPosterMaxim(dto));
    }

    @ApiOperation(value = "获取课程分享信息", notes = "获取课程分享信息")
    @PostMapping("/getShareInfo")
    public ResultInfo<CourseShareVO> getShareInfo(@RequestBody @Validated CourseShareDTO dto, BindingResult bindingResult,
                                                  @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        return ResultInfo.ok(courseShareService.getShareInfo(dto, headerUserInfo));
    }

    /**************************************以下为平移过来的接口暂未使用***************************************/

    @ApiOperation(value = "我的证书", notes = "我的证书")
    @PostMapping("/myCertificateInfoList")
    public ResultInfo<List<CertificateInfoVO>> myCertificateInfoList() {
        Integer userId = null; // TODO: 2021/10/26 调用用户中心获取userid
        return ResultInfo.ok(certificateInfoService.myCertificateInfoList(userId));
    }

    @ApiOperation(value = "我的手册", notes = "我的手册")
    @PostMapping("/myStudyManualList")
    public ResultInfo<List<StudyManualListVO>> myStudyManualList() {
        Integer userId = null; // TODO: 2021/10/26 调用用户中心获取userid
        return ResultInfo.ok(certificateInfoService.myStudyManualList(userId));
    }

    @ApiOperation(value = "我的手册详情", notes = "我的手册详情")
    @PostMapping("/myStudyManualDetail")
    public ResultInfo<StudyManualVO> myStudyManualDetail(@RequestBody @Validated StudyManualSearchDTO studyManualSearchDTO,
                                                         BindingResult bindingResult) {
        return ResultInfo.ok(certificateInfoService.myStudyManualDetail(studyManualSearchDTO));
    }

    @ApiOperation(value = "banner列表", notes = "banner列表")
    @PostMapping("/bannerList")
    public ResultInfo<List<BannerConfigVO>> bannerList(@RequestBody BannerSearchDTO bannerSearchDto, HttpServletRequest request) {
        return ResultInfo.ok(certificateInfoService.bannerList(bannerSearchDto));
    }

    @ApiOperation(value = "商品分享信息-分享语", notes = "商品分享信息-分享语")
    @PostMapping("/getShareWords")
    public ResultInfo<CourseShareWordsVO> getShareWords(@RequestBody @Validated CourseShareDTO dto, HttpServletRequest request,
                                                        BindingResult bindingResult) {
        return ResultInfo.ok(courseShareService.getShareWords(dto));
    }

    @ApiOperation(value = "添加意见反馈", notes = "添加意见反馈")
    @PostMapping("/feedback/add")
    public ResultInfo addUserFeedback(@RequestBody FeedbackAddDTO dto, HttpServletRequest request) {
        // TODO: 2021/10/27 待获取用户信息
//        UserInfoVO loginUserInfo = redisUtil.getLoginUserInfo(request);
        userFeedbackService.addUserFeedback(dto);
        return ResultInfo.ok();
    }

    @ApiOperation(value = "反馈通知", notes = "反馈通知")
    @GetMapping("/feedback/news")
    public ResultInfo<FeedBackNewsVO> feedBackNews(HttpServletRequest request) {
        // TODO: 2021/10/27 待获取用户信息
//        UserInfoVO loginUserInfo = redisUtil.getLoginUserInfo(request);
        return ResultInfo.ok(userFeedbackService.feedBackNews(null, ""));
    }

    @ApiOperation(value = "获取我的意见反馈列表", notes = "获取我的意见反馈列表")
    @PostMapping("/feedback/list")
    public ResultInfo<List<FeedbackListVO>> myFeedbackList(@RequestBody FeedbackSearchDTO dto, HttpServletRequest request) {
        // TODO: 2021/10/27 待获取用户信息
//        UserInfoVO loginUserInfo = redisUtil.getLoginUserInfo(request);
        return ResultInfo.ok(userFeedbackService.getMyFeedBackList(dto));
    }

    @ApiOperation(value = "获取反馈详情", notes = "获取反馈详情")
    @PostMapping("/feedback/detail")
    public ResultInfo<FeedbackDetailVO> feedbackDetail(@RequestBody FeedbackSearchDTO dto, HttpServletRequest request) {
        // TODO: 2021/10/27 待获取用户信息
//        UserInfoVO loginUserInfo = redisUtil.getLoginUserInfo(request);
        return ResultInfo.ok(userFeedbackService.feedbackDetail(dto));
    }

    @PostMapping(value = "/app/pendantConfig")
    @ApiOperation(value = "app-挂件配置列表", notes = "app-挂件配置列表")
    public ResultInfo<List<PendantConfigVO>> info(@RequestBody PendantConfigSearchAppDTO dto, HttpServletRequest request){
        return ResultInfo.ok(pendantConfigService.getAppPendantConfig(dto));
    }

    @PostMapping(value = "/h5/pendantConfig")
    @ApiOperation(value = "H5-挂件配置列表", notes = "H5-挂件配置列表")
    public ResultInfo<List<PendantConfigVO>> info(@RequestBody PendantConfigSearchH5DTO dto, HttpServletRequest request){
        return ResultInfo.ok(pendantConfigService.getH5PendantConfig(dto));
    }


}
