package com.sibuqu.common.enums.weixin;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @date  2024-05-13 17:45
 * 发送对象 1:数据标签 2:课程
 */
public enum SendObjectEnum {
	/**
	 * 数据标签
	 */
	TAG(1,"数据标签"),
	/**
	 * 商品
	 */
	GOODS(2,"商品"),

	;

	private Integer code;
    private String value;

    SendObjectEnum(Integer code, String value) {
		this.code=code;
		this.value=value;
	}

	public Integer getCode() {
		// TODO Auto-generated method stub
		return this.code;
	}

	public String getValue() {
		return this.value;
	}

	public static SendObjectEnum get(Integer code) {
		for (SendObjectEnum model : EnumSet.allOf(SendObjectEnum.class)) {
			if (model.code==code.intValue()) {
				return model;
			}
		}
		return null;
	}

	public static SendObjectEnum get(String value){
		for (SendObjectEnum model : EnumSet.allOf(SendObjectEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
	}

}
