package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/12/04 14:56
 * @Description 小程序的类型
 */
@Getter
@AllArgsConstructor
public enum MiniTypeEnum {
    MINI(10,"miniapp"),//润泽园小程序
    WORLD_MINI(15,"world_mini"),//天地常新微信小程序
    ;


    private Integer code;
    private String value;
    public static MiniTypeEnum getByCode(Integer code){
        for (MiniTypeEnum value : MiniTypeEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
