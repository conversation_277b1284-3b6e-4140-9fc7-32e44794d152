package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @Date 2020/8/30 15:19
 * @Version 1.0
 * @Description 发送状态
 **/
public enum SendStatusEnum {
    /**
     *  未发送
     */
    NO(0,"未发送"),
    /**
     *  已发送
     */
    YES(1,"已发送"),
    ;
    private Integer code;
    private String value;

    private SendStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        // TODO Auto-generated method stub
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    public static SendStatusEnum get(Integer code) {
        for (SendStatusEnum model : EnumSet.allOf(SendStatusEnum.class)) {
            if (model.code==code.intValue()) {
                return model;
            }
        }
        return null;
    }

    public static SendStatusEnum get(String value){
        for (SendStatusEnum model : EnumSet.allOf(SendStatusEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
    }

}
