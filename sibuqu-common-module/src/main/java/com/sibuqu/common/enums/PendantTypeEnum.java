package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/10/27 15:46
 * @Description 挂件的类型枚举
 */
@Getter
@AllArgsConstructor
public enum PendantTypeEnum {

    /**
     * 课程类型
     */
    COURSE(1,"课程类型"),
    /**
     * 课件类型
     */
    RES(2,"课件类型"),
  ;

    /**
     * code
     */
    private Integer code;
    /**
     * value
     */
    private String value;
    public static PendantTypeEnum getByCode(Integer code){
        for (PendantTypeEnum value : PendantTypeEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
