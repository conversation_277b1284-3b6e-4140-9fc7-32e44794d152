package com.sibuqu.common.enums;

public class AppDialogBoxEnum {

    public enum PositionTypeEnum {
        APP_HOME_PAGE(1, "APP首页"),
        GOODS_DETAILS(2, "商品详情页"),
        COURSE_PLAY(3, "课程播放页"),
        LIVE(4, "独立直播"),
        STUDY(5, "学习页"),
        PERSONAL_CENTER(6, "个人中心页"),
        CLASS_HOME_PAGE(7, "班级主页");

        final Integer type;
        final String desc;

        PositionTypeEnum(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public Integer getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum ShowStatusEnum {
        PRE_DISPLAY(1, "待展示"),
        ALREADY_DISPLAY(2, "已展示"),
        SHELVES(3, "下架");

        final Integer type;
        final String desc;

        ShowStatusEnum(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public Integer getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum JumpTypeEnum {
        INTERNAL(1, "内部链接"),
        GOODS_DETAILS(2, "商品详情页"),
        COURSE_PLAY(3, "课程播放页"),
        NOT_JUMP(4, "不跳转");

        final Integer type;
        final String desc;

        JumpTypeEnum(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public Integer getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }
    }
    public enum PushPersonTypeEnum {
        ALL(1, "全部用户"),
        BY_COURSE(2, "按照课程权限"),
        EXCEL_PERSON(3, "指定用户");

        final Integer type;
        final String desc;

        PushPersonTypeEnum(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public Integer getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }
    }



}
