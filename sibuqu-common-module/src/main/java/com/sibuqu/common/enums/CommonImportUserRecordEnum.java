package com.sibuqu.common.enums;

import lombok.Getter;

public class CommonImportUserRecordEnum {

    @Getter
    public enum DataType {
        POPUP(1, "弹窗"),
        ADS(2, "广告位");

        DataType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        final Integer code;
        final String desc;
    }

    @Getter
    public enum ImportStatus {
        SUCCESS(1, "成功"),
        FAIL(0, "失败");

        ImportStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        final Integer code;
        final String desc;
    }

    @Getter
    public enum FailCode {
        USER_ID_NOT_EXIST(1, "用户ID不存在"),
        USER_ID_INPUT_ERROR(2, "用户ID输入错误不符合要求"),
        USER_ID_REPEAT(3, "用户ID重复");

        FailCode(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        final Integer code;
        final String desc;
    }

}
