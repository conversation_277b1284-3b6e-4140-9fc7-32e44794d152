package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @Date 2021/1/5 14:13
 * @Version 1.0
 * @Description 是否已发送
 **/
public enum IsSendEnum {
    /**
     * 未发送
     */
    NO(0,"未发送"),
    YES(1,"已发送");
    /**
     * code
     */
    private Integer code;
    /**
     * value
     */
    private String value;

    IsSendEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        // TODO Auto-generated method stub
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    public static IsSendEnum get(Integer code) {
        for (IsSendEnum model : EnumSet.allOf(IsSendEnum.class)) {
            if (model.code==code.intValue()) {
                return model;
            }
        }
        return null;
    }

    public static IsSendEnum get(String value){
        for (IsSendEnum model : EnumSet.allOf(IsSendEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
    }
}
