package com.sibuqu.common.enums;

public enum PageTypeEnum {

    ALL(0, "全部"),
    HOME_PAGE(1, "首页"),
    COLUMN_PAGE(2, "栏目页面"),
    CONTENT_PAGE(3, "内容页面"),
    INDEPENDENT_PAGE(4, "独立页面"),
    ;
    private Integer type;
    private String desc;

    PageTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
