package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/10/25 17:56
 * @Description 评论发送类型枚举
 */
@Getter
@AllArgsConstructor
public enum CommentSendTypeEnum {
    /**
     * 直播或者录播下的AI自动判断未通过
     */
    COMMENT (1,"评论"),
    /**
     * AI自动判断通过
     */
    REPLY(2,"回复");


    private Integer code;
    private String value;
    public static CommentSendTypeEnum getByCode(Integer code){
        for (CommentSendTypeEnum value : CommentSendTypeEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
