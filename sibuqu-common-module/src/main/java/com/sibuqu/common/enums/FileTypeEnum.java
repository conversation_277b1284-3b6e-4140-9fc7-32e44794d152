package com.sibuqu.common.enums;

/**
 * 文件类型
 *
 * <AUTHOR>
 * @date 2021-10-15 09:37:58
 */
public enum FileTypeEnum {
    IMAGE(1, "图片"),
    VIDEO(2, "视频");

    private Integer code;
    private String desc;

    FileTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    public Integer getCode() {
        return this.code;
    }

    public static String getDesc(Integer code) {
        for (FileTypeEnum FileTypeEnum : FileTypeEnum.values()) {
            if (FileTypeEnum.getCode().equals(code)) {
                return FileTypeEnum.desc;
            }
        }
        return null;
    }
}
