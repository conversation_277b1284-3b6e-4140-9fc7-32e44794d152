package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Date 2020/8/27 16:43
 * @Version 1.0
 * @Description redis key
 **/
@Getter
@AllArgsConstructor
public enum RedisKeyEnum {
    /**
     * 课件
     */
    COURSE_RES ("course_res:course_res_","课件"),
    COURSE_LIST ("course_res_list:","课件列表"),

    COURSE_RES_NOTICE_INFO("course_res_notice_info:resId_","课件的公告信息"),
    /**
     * 商品
     */
    GOODS_SKU("goods_sku_","商品"),
    /**
     * 搜索课件
     */
    HOMEPAGE_SEARCH("homepage:search","搜索课件"),
    WARE_PLAY("w_pl_","播放量"),
    WARE_PRAISE("w_pr_","点赞量"),
    APP_GOODS_LIST_CACHE("app_goods_list_cache","首页楼层缓存"),
    ;

    /**
     * code
     */
    private String code;
    /**
     * value
     */
    private String value;
    public static RedisKeyEnum getByCode(Integer code){
        for (RedisKeyEnum value : RedisKeyEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
