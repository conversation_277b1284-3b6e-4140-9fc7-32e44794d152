package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/10/25 17:56
 * @Description 推送消息类型枚举
 */
@Getter
@AllArgsConstructor
public enum CommonPushTypeEnum {
    SYS_MSG(1,"系统消息"),
    ACTIVITY_MSG(2,"活动消息"),
    COMMON_MSG(3,"评论和回复消息"),
    LIKE_MSG(4,"点赞消息"),
    STUDY_MSG(5,"学习提醒"),
    ORDER_MSG(7,"订单消息"),
    MEETING_MSG(8,"会议消息"),
    OPEN_VIP_MSG(9,"开通VIP通知"),
    ;

    private Integer code;
    private String value;
    public static CommonPushTypeEnum getByCode(Integer code){
        for (CommonPushTypeEnum value : CommonPushTypeEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
