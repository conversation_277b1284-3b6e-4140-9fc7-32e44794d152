package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/10/25 17:56
 * @Description 分享类型枚举
 */
public enum CourseShareTypeEnum {

    COURSE_SHARE(1,"课程分享"),
    LIVE_SHARE(2,"独立直播分享"),
    RECEIVE_SHARE(3,"兑换码分享");

    private Integer code;

    private String value;

    CourseShareTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return this.code;
    }

    public static CourseShareTypeEnum getByCode(Integer code){
        for (CourseShareTypeEnum value : CourseShareTypeEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
