package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/12/08 11:56
 * @Description 课程或班级分享图类型枚举 海报类型（1.课程海报  2.邀请加班海报 3.战略APP课程海报 4.日收获海报）
 */
@Getter
@AllArgsConstructor
public enum CoursePosterShareTypeEnum {

    COURSE_POSTER (1,"课程海报"),
    CLASS_POSTER(2,"邀请加班海报"),
    INNO_COURSE_POSTER(3,"战略APP课程海报"),
    INNO_WORK_SHARE_POSTER(4,"战略APP日收获海报");


    private Integer code;
    private String value;
    public static CoursePosterShareTypeEnum getByCode(Integer code){
        for (CoursePosterShareTypeEnum value : CoursePosterShareTypeEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
