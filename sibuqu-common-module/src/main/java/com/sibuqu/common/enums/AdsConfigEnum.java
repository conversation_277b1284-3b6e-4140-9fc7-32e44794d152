package com.sibuqu.common.enums;

import lombok.Getter;

public class AdsConfigEnum {

    public enum JumpType {
        NOT_JUMP(1, "不跳转"),
        CUSTOM_LINK(2, "自定义连接"),
        GOODS(3, "商品"),
        COURSE(4, "课件"),
        LIVE(5, "独立直播"),
        MINI_MEETING(6, "小程序会议页"),
        ENTERPRISE_SPACE(7, "企业空间"),
        LEARN_MASTER(16, "请教阳明先生"),
        AI_COLLECTION_BOX(19, "AI收纳箱");

        @Getter
        final Integer code;
        final String desc;

        JumpType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }

    @Getter
    public enum ShowType {
        INDEX_TOP(1, "首页顶部banner"),
        INDEX_MIDDLE(2, "首页中部广告位"),
        ALL_COURSE_TOP(3, "全部课程顶部"),
        MY_AD(4, "我的广告位"),
        PERSONAL_HOMEPAGE(5, "个人主页"),
        INDEX_ZONE_TOP(6, "首页专区顶部banner"),
        INDEX_BOTTOM(7, "首页底部banner"),
        GOLDEN_SENTENCE_BOTTOM(8, "每日金句底部"),
        APP_OPEN_PAGE(9, "app开屏页");

        final Integer code;
        final String desc;

        ShowType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }


    @Getter
    public enum VisibleRange {
        ALL(1, "所有人"),
        COURSE_PERMISSION(2, "按课程权限"),
        COMPANY_USER(3, "按企业用户"),
        SPECIFIED_USER(4, "指定用户"),
        NEW_REGISTERED_USER(5, "新注册用户"),
        ;

        final Integer code;
        final String desc;

        VisibleRange(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }


    @Getter
    public enum LoadStatus {
        UP(1, "上架"),
        DOWN(0, "下架");

        final Integer code;
        final String desc;

        LoadStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }

}
