package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @date  2024-12-18 17:31
 * 消息类别：1:文本，2:表情，3:图片 4:音频 5:视频 6:文件 7:自定义
 */
public enum ImMsgTypeEnum {
	/**
	 * 文字
	 */
	TEXT(1,"文本"),
	/**
	 * 表情
	 */
	EMOTE(2,"表情"),
	/**
	 * 图片
	 */
	IMAGE(3,"图片"),
	/**
	 * 音频
	 */
	AUDIO(4,"音频"),

	/**
	 * 视频
	 */
	VIDEO(5,"视频"),
	/**
	 * 文件
	 */
	FILE(6,"文件"),
	/**
	 * 自定义
	 */
	CUSTOM(7,"自定义"),
	;

	private Integer code;
    private String value;

    ImMsgTypeEnum(Integer code, String value) {
		this.code=code;
		this.value=value;
	}

	public Integer getCode() {
		// TODO Auto-generated method stub
		return this.code;
	}

	public String getValue() {
		return this.value;
	}



	public static ImMsgTypeEnum get(String value){
		for (ImMsgTypeEnum model : EnumSet.allOf(ImMsgTypeEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
	}


}
