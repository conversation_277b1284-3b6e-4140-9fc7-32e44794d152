package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/10/25 17:56
 * @Description 评论发送类型枚举
 */
@Getter
@AllArgsConstructor
public enum TencentContentAuditMediaTypeEnum {
    TEXT (1,"文本"),
    IMAGE(2,"图片"),
    AUDIO(3,"音频"),
    VIDEO(4,"视频"),
    ;


    private Integer code;
    private String value;
    public static TencentContentAuditMediaTypeEnum getByCode(Integer code){
        for (TencentContentAuditMediaTypeEnum value : TencentContentAuditMediaTypeEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
