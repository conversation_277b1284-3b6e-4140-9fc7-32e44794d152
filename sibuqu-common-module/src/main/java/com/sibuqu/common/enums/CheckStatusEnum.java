package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/8/27 16:43
 * @Version 1.0
 * @Description 直播下的审批状态
 **/
@Getter
@AllArgsConstructor
public enum CheckStatusEnum {
    /**
     * 直播或者录播下的AI自动判断未通过
     */
    AUTO_AUDIT_FAILED(0, "AI自动判断未通过", 1, "待审核"),
    /**
     * AI自动判断通过
     */
    AUTO_AUDIT_REVIEWED(1, "AI自动判断通过", 2, "审核通过"),
    /**
     * 人工审核未通过
     */
    AUDIT_FAILED(2, "人工审核未通过", 3, "审核不通过"),
    /**
     * 人工审核通过
     */
    REVIEWED(3, "人工审核通过", 2, "审核通过");

    private final Integer code;
    private final String value;
    private final Integer backStatus;
    private final String backStatusName;

    public static CheckStatusEnum getByCode(Integer code) {
        for (CheckStatusEnum value : CheckStatusEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<Integer> getCodeBybackStatus(Integer backStatus) {
        List<Integer> list = new ArrayList<>();
        for (CheckStatusEnum value : CheckStatusEnum.values()) {
            if (value.backStatus.equals(backStatus)) {
                list.add(value.code);
            }
        }
        return list;
    }
}
