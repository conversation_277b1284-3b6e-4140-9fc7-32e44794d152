package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/10/25 17:56
 * @Description 意见反馈老师回复状态枚举
 */
@Getter
@AllArgsConstructor
public enum FeedBackReplyEnum {

    NO_REPLY(0, "老师未回复"),

    YES_REPLY(1, "老师已回复");

    private Integer code;
    private String value;

    public static FeedBackReplyEnum getByCode(Integer code) {
        for (FeedBackReplyEnum value : FeedBackReplyEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
