package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class FunctionItemEnum {

    @AllArgsConstructor
    public enum JumpType {
        H5(1, "H5地址"),
        APP(2, "APP协议"),
        COURSE_WARE(3, "课件"),
        ;

        @Getter
        private final Integer code;
        private final String desc;

    }

    @AllArgsConstructor
    public enum UpStatus {
        UP(1, "上架"),
        DOWN(0, "下架"),
        ;

        @Getter
        private final Integer code;
        private final String desc;

    }
}
