package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @date  2024-05-14 11:45
 * 模板跳转类型  0:不跳转页面 1:H5页面链接 2:小程序链接
 */
public enum TempJumpTypeEnum {
	/**
	 * H5页面链接
	 */
	H5(1,"H5页面链接"),

	/**
	 * 小程序链接
	 */
	MINI(2,"小程序链接"),

	/**
	 * 不跳转页面
	 */
	NO_JUMP(0,"不跳转页面"),


	;

	private Integer code;
    private String value;

    TempJumpTypeEnum(Integer code, String value) {
		this.code=code;
		this.value=value;
	}

	public Integer getCode() {
		// TODO Auto-generated method stub
		return this.code;
	}

	public String getValue() {
		return this.value;
	}

	public static TempJumpTypeEnum get(Integer code) {
		for (TempJumpTypeEnum model : EnumSet.allOf(TempJumpTypeEnum.class)) {
			if (model.code==code.intValue()) {
				return model;
			}
		}
		return null;
	}

	public static TempJumpTypeEnum get(String value){
		for (TempJumpTypeEnum model : EnumSet.allOf(TempJumpTypeEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
	}

}
