package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @Date 2021/7/17 15:19
 * @Version 1.0
 * @Description 是否置顶
 **/
public enum IsTopEnum {
    /**
     * 未置顶
     */
    NO(0,"未置顶"),
    /**
     * 置顶
     */
    YES(1,"置顶");

    /**
     * code
     */
    private Integer code;
    /**
     * value
     */
    private String value;

    private IsTopEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        // TODO Auto-generated method stub
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    public static IsTopEnum get(Integer code) {
        for (IsTopEnum model : EnumSet.allOf(IsTopEnum.class)) {
            if (model.code==code.intValue()) {
                return model;
            }
        }
        return null;
    }

    public static IsTopEnum get(String value){
        for (IsTopEnum model : EnumSet.allOf(IsTopEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
    }

}
