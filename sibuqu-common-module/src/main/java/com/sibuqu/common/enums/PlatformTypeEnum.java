package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2022年5月17日18:13:21
 * @Description 平台类型枚举
 */
@Getter
@AllArgsConstructor
public enum PlatformTypeEnum {

    ENTERPRISE (0,"企业版"),
    PERSONAL(1,"个人版"),
    MINI_PROGRAM(2,"小程序");

    private Integer code;
    private String value;
    public static PlatformTypeEnum getByCode(Integer code){
        for (PlatformTypeEnum value : PlatformTypeEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
