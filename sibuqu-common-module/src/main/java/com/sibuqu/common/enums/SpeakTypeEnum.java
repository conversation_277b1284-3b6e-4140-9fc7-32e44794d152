package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 *  禁言或者解除禁言的范围
 *  0：本次解除禁言（禁言）
 *  1：全程解除禁言 (禁言)
 */
public enum SpeakTypeEnum {
	/**
	 * 0 本场直播进行 解除禁言（禁言）
	 */
	THIS(0,"all"),
	/**
	 * 有效
	 */
	ALL(1,"this"),
	;

	private Integer code;
    private String value;

    SpeakTypeEnum(Integer code, String value) {
		this.code=code;
		this.value=value;
	}
	public Integer getCode() {
		// TODO Auto-generated method stub
		return this.code;
	}

	public String getValue() {
		return this.value;
	}

	public static SpeakTypeEnum get(Integer code) {
        for (SpeakTypeEnum model : EnumSet.allOf(SpeakTypeEnum.class)) {
            if (model.code==code.intValue()) {
                return model;
            }
        }
        return null;
    }

	public static SpeakTypeEnum get(String value){
		for (SpeakTypeEnum model : EnumSet.allOf(SpeakTypeEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
	}


}
