package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2022年5月31日14:51:50
 * @Description 数据标签类型枚举
 */
@Getter
@AllArgsConstructor
public enum DataLabelTypeEnum {

    CLASSES_LABEL(1,"班级标签");

    private Integer code;
    private String value;
    public static DataLabelTypeEnum getByCode(Integer code){
        for (DataLabelTypeEnum value : DataLabelTypeEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
