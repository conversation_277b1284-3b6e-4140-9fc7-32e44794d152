package com.sibuqu.common.enums;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @Description 数据状态枚举
 * @CreateTime 2021年12月20日 08:49:00
 */
public enum DataFlagEnum {
    SHOW(1, "显示"),
    DISABLE(2, "禁用");

    private Integer code;
    private String desc;

    DataFlagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    public Integer getCode() {
        return this.code;
    }

    public static String getDesc(Integer code) {
        for (DataFlagEnum dataFlagEnum : DataFlagEnum.values()) {
            if (dataFlagEnum.getCode().equals(code)) {
                return dataFlagEnum.desc;
            }
        }
        return null;
    }
}
