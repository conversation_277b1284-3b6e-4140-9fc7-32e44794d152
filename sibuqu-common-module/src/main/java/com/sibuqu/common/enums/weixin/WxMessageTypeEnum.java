package com.sibuqu.common.enums.weixin;

import lombok.Data;
import lombok.Getter;

/**
 * @ClassName WxMessageTypeEnum.java
 * <AUTHOR>
 * @Version 1.0.0
 * @Description 微信公众号 服务号 用户操作指令，是动作事情节点还是文本信息
 * @CreateTime 2024年04月29日 09:50:47
 */
@Getter
public enum WxMessageTypeEnum {
    /**
     * 事件类型，比如订阅与取消订阅 扫描等
     */
    EVENT("event"),
    /**
     *向公众号发送的文字消息
     */
    TEXT("text"),
    ;

    private String desc;

    WxMessageTypeEnum(String desc) {
        this.desc = desc;
    }
}
