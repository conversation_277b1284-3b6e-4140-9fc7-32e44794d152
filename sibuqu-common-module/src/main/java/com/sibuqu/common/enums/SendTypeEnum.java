package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @date  2022-09-15 16:45
 * 操作类型0不跳转1H5页面2商品3课件
 */
public enum SendTypeEnum {
	/**
	 * 不跳转
	 */
	ALL(0,"不跳转"),

	/**
	 * h5页面
	 */
	COURSE_RIGHT(1,"h5页面"),
	SPECIFY_USER(2,"指定用户");

	private Integer code;
    private String value;

    SendTypeEnum(Integer code, String value) {
		this.code=code;
		this.value=value;
	}

	public Integer getCode() {
		// TODO Auto-generated method stub
		return this.code;
	}

	public String getValue() {
		return this.value;
	}

	public static SendTypeEnum get(Integer code) {
		for (SendTypeEnum model : EnumSet.allOf(SendTypeEnum.class)) {
			if (model.code==code.intValue()) {
				return model;
			}
		}
		return null;
	}

	public static SendTypeEnum get(String value){
		for (SendTypeEnum model : EnumSet.allOf(SendTypeEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
	}

}
