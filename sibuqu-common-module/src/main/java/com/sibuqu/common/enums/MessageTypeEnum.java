package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @date  2021-06-17 10:45
 * 消息类型1推送2短信3站内信
 */
public enum MessageTypeEnum {
	/**
	 * 不跳转
	 */
	PUSH(1,"推送"),

	/**
	 * 短信
	 */
	SMS(2,"短信"),
	/**
	 * 站内信
	 */
	LOCAL_INFO(3,"站内信"),
	;

	private Integer code;
    private String value;

    MessageTypeEnum(Integer code, String value) {
		this.code=code;
		this.value=value;
	}

	public Integer getCode() {
		// TODO Auto-generated method stub
		return this.code;
	}

	public String getValue() {
		return this.value;
	}

	public static MessageTypeEnum get(Integer code) {
		for (MessageTypeEnum model : EnumSet.allOf(MessageTypeEnum.class)) {
			if (model.code==code.intValue()) {
				return model;
			}
		}
		return null;
	}

	public static MessageTypeEnum get(String value){
		for (MessageTypeEnum model : EnumSet.allOf(MessageTypeEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
	}

}
