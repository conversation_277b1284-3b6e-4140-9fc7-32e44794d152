package com.sibuqu.common.enums;

public enum ButtonJumpEnum {

    URL(1, "URL"),
    COURSE_DETAIL(2, "课程详情页"),
    COURSE_PAY(3, "课程购买支付页"),
    EXCHANGE_CODE_PAY(4, "兑换码购买支付页"),
    LIVE(5, "独立直播"),
    DOWN_PICTURE(6, "下载图片"),
    ;
    private final Integer type;
    private final String desc;

    ButtonJumpEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
