package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @date  2021-05-13 17:12
 * 回复的类型  0文本，1图片 3.文字与图片 4.公告消息
 */
public enum TeacherReplyEnum {


	/**
	 * 文字
	 */
	TEXT(0,"文本"),
	/**
	 * 图片
	 */
	IMAGE(1,"图片"),
	/**
	 * 文字与图片
	 */
	TEXT_IMAGE(3,"图文消息"),
	TEXT_NOTICE(4,"公告消息"),
	;

	private Integer code;
    private String value;

    TeacherReplyEnum(Integer code, String value) {
		this.code=code;
		this.value=value;
	}

	public Integer getCode() {
		// TODO Auto-generated method stub
		return this.code;
	}

	public String getValue() {
		return this.value;
	}



	public static TeacherReplyEnum get(String value){
		for (TeacherReplyEnum model : EnumSet.allOf(TeacherReplyEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
	}


}
