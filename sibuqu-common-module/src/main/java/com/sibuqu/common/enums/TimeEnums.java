package com.sibuqu.common.enums;

import com.baomidou.mybatisplus.core.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 * @date 2021/7/28-下午2:29
 */
public enum TimeEnums implements IEnum<Integer> {
    JUST_NOW(0,"一分钟以内","刚刚"),
    MANY_MINUTES(1,"大于一分钟小于60分钟","分钟前"),
    MANY_HOUR(2,"大于60分钟小于24小时","小时前"),
    MANY_DAY(3,"大于24小时，显示具体时间","月日时间"),
    MANY_YEAR(4,"超过一年,显示年月日","年月日时间")
    ;

    TimeEnums(Integer value, String name, String title) {
        this.value = value;
        this.name = name;
        this.title = title;
    }

    @JsonValue
    @JsonFormat
    private Integer value;
    private String name;
    private String title;
    @Override
    public Integer getValue() {
        return value;
    }

    public String getTitle(){
        return title;
    }
}
