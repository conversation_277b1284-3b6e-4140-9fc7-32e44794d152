package com.sibuqu.common.enums;

public class AdsEnum {

    public enum EditionType {
        PERSONAL(1, "个人版"),
        COMPANY(2, "所有企业"),
        SINGLE_COMPANY(3, "单个企业"),
        MINI_PROGRAM(4, "小程序")
        ;

        final Integer type;
        final String desc;

        EditionType(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public Integer getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }
    }


}
