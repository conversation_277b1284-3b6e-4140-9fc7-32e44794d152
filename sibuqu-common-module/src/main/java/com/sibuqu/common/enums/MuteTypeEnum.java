package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @Date 2021/6/21 15:19
 * @Version 1.0
 * @Description '0:本场禁言 1:全部禁言
 **/
public enum MuteTypeEnum {
    /**
     * 本次禁言
     */
    THIS(0,"本次禁言"),
    /**
     * 全部禁言
     */
    ALL(1,"全部禁言");

    /**
     * code
     */
    private Integer code;
    /**
     * value
     */
    private String value;

    private MuteTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        // TODO Auto-generated method stub
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    public static MuteTypeEnum get(Integer code) {
        for (MuteTypeEnum model : EnumSet.allOf(MuteTypeEnum.class)) {
            if (model.code==code.intValue()) {
                return model;
            }
        }
        return null;
    }

    public static MuteTypeEnum get(String value){
        for (MuteTypeEnum model : EnumSet.allOf(MuteTypeEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
    }

}
