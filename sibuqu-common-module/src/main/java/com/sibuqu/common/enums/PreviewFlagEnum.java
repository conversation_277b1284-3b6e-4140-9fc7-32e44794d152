package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @date  2021-06-17 10:45
 * 操作类型0不跳转1H5页面2商品3课件
 */
public enum PreviewFlagEnum {
	/**
	 * 不跳转
	 */
	UN_PREVIEW(0,"非预览"),

	/**
	 * h5页面
	 */
	PREVIEW(1,"预览"),
	;

	private Integer code;
    private String value;

    PreviewFlagEnum(Integer code, String value) {
		this.code=code;
		this.value=value;
	}

	public Integer getCode() {
		// TODO Auto-generated method stub
		return this.code;
	}

	public String getValue() {
		return this.value;
	}

	public static PreviewFlagEnum get(Integer code) {
		for (PreviewFlagEnum model : EnumSet.allOf(PreviewFlagEnum.class)) {
			if (model.code==code.intValue()) {
				return model;
			}
		}
		return null;
	}

	public static PreviewFlagEnum get(String value){
		for (PreviewFlagEnum model : EnumSet.allOf(PreviewFlagEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
	}

}
