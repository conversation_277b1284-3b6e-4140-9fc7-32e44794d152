package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @date  2022-11-29 13:58
 * 敏感词的枚举类
 * 敏感词所属的类型  0:适用于所有 1：只适用于直播 2：只适用于视频/音频
 */
public enum SensitiveWordsTypeEnum {
	/**
	 * 适用于所有
	 */
	ALL(0,"适用于所有"),

	/**
	 * 只适用于直播
	 */
	LIVE(1,"只适用于直播"),
	/**
	 * 只适用于视频/音频
	 */
	VIDEO(2,"只适用于视频/音频"),

	MINIAPP_NICKNAME(3,"小程序昵称校验"),
	;

	private Integer code;
    private String value;

    SensitiveWordsTypeEnum(Integer code, String value) {
		this.code=code;
		this.value=value;
	}

	public Integer getCode() {
		// TODO Auto-generated method stub
		return this.code;
	}

	public String getValue() {
		return this.value;
	}

	public static SensitiveWordsTypeEnum get(Integer code) {
		for (SensitiveWordsTypeEnum model : EnumSet.allOf(SensitiveWordsTypeEnum.class)) {
			if (model.code==code.intValue()) {
				return model;
			}
		}
		return null;
	}

	public static SensitiveWordsTypeEnum get(String value){
		for (SensitiveWordsTypeEnum model : EnumSet.allOf(SensitiveWordsTypeEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
	}

}
