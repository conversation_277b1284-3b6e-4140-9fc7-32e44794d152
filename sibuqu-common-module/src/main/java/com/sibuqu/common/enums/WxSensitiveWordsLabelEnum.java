package com.sibuqu.common.enums;

import java.util.EnumSet;

public enum WxSensitiveWordsLabelEnum {

	NORMAL(100,"正常"),
	GUAN_GAGO(10001,"广告"),
	SHI_ZHENG(20001,"时政"),
	SE_QING(20002,"色情"),
	RU_MA(20003,"辱骂"),
	FAN_ZUI(20006,"违法犯罪"),
	QI_ZHA(20008,"欺诈"),
	DI_SU(20012,"低俗"),
	BAN_QUAN(20013,"版权"),
	OTHER(21000,"其他"),
	;

	private Integer code;
    private String value;

    WxSensitiveWordsLabelEnum(Integer code, String value) {
		this.code=code;
		this.value=value;
	}

	public Integer getCode() {
		// TODO Auto-generated method stub
		return this.code;
	}

	public String getValue() {
		return this.value;
	}

	public static WxSensitiveWordsLabelEnum get(Integer code) {
		for (WxSensitiveWordsLabelEnum model : EnumSet.allOf(WxSensitiveWordsLabelEnum.class)) {
			if (model.code==code.intValue()) {
				return model;
			}
		}
		return null;
	}

	public static WxSensitiveWordsLabelEnum get(String value){
		for (WxSensitiveWordsLabelEnum model : EnumSet.allOf(WxSensitiveWordsLabelEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
	}

}
