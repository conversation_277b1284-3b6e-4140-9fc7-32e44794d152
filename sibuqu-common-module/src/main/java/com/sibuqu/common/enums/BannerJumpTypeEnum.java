package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/10/25 17:56
 * @Description banner跳转类型枚举
 */
@Getter
@AllArgsConstructor
public enum BannerJumpTypeEnum {

    CUSTOMER(0, "自定义连接"),

    COURSE(1, "课程"),

    RESOURCE(2, "课件"),

    LIVE(3, "直播"),

    TEXT(4, "图文内容");


    private Integer code;
    private String value;

    public static BannerJumpTypeEnum getByCode(Integer code) {
        for (BannerJumpTypeEnum value : BannerJumpTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
