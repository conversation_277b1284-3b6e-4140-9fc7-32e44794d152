package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @date  2021-06-17 10:45
 * mqtt发送的枚举
 */
public enum LiveMqttOperEnum {

	/**
	 * 直播 需要发送mqtt 标识的
	 */
	/**
	 * 直播状态开始
	 */
	LIVE_STATUS_START("liveAloneStartMsg","直播状态开始"),

	/**
	 * 直播状态结束
	 */
	LIVE_STATUS_END("liveAloneEndMsg","直播状态结束"),

	;

	private String code;
    private String value;

    LiveMqttOperEnum(String code, String value) {
		this.code=code;
		this.value=value;
	}

	public String getCode() {
		// TODO Auto-generated method stub
		return this.code;
	}

	public String getValue() {
		return this.value;
	}

	public static LiveMqttOperEnum getByCode(String code) {
		for (LiveMqttOperEnum model : EnumSet.allOf(LiveMqttOperEnum.class)) {
			if (model.code.equals(code)) {
				return model;
			}
		}
		return null;
	}

	public static LiveMqttOperEnum ByValue(String value){
		for (LiveMqttOperEnum model : EnumSet.allOf(LiveMqttOperEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
	}

}
