package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/10/25 17:56
 * @Description 课程箴言类型枚举
 */
@Getter
@AllArgsConstructor
public enum CoursePosterMaximTypeEnum {

    POSTER(1,"海报箴言"),
    CLASSICS_HIT(2,"经典撞击"),
    NEW_WORD_GOLDEN_SENTENCE(3,"天地常新金句");

    private Integer code;
    private String value;
    public static CoursePosterMaximTypeEnum getByCode(Integer code){
        for (CoursePosterMaximTypeEnum value : CoursePosterMaximTypeEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
