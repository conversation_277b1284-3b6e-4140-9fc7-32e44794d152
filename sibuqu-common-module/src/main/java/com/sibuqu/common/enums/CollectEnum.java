package com.sibuqu.common.enums;

public class CollectEnum {

    public enum DataTypeEnum {
        VIDEO_EXPERIENCE(1, "小程序视频心得"),
        VIDEO_GROUP(12, "小组视频");

        private Integer code;
        private String name;

        DataTypeEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public static boolean contains(Integer code) {
            for (DataTypeEnum dataTypeEnum : DataTypeEnum.values()) {
                if (dataTypeEnum.getCode().equals(code)) {
                    return true;
                }
            }
            return false;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

}
