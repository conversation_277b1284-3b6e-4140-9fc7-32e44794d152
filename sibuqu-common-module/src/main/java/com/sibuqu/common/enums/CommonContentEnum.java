package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class CommonContentEnum {

    @AllArgsConstructor
    public enum DataType {
        COURSE(1, "课程下班级内容"),
        SQUARE(2, "广场"),
        INNO_ZONE(3, "inno专区"),
        INNO_WEEK_WORK(4, "inno周作业");

        @Getter
        private final Integer code;
        private final String desc;

    }

    @AllArgsConstructor
    public enum ContentBOType {
        TEXT(1, "文本"),
        IMAGE(2, "图片"),
        VIDEO(3, "视频"),
        AUDIO(4, "语音");
        @Getter
        private final Integer code;
        private final String desc;

    }

    @AllArgsConstructor
    public enum ContentType {
        VIDEO(1, "视频"),
        IMAGE(2, "图片(包含纯文本)"),
        AUDIO(3, "语音");
        @Getter
        private final Integer code;
        private final String desc;

    }

    @AllArgsConstructor
    public enum TopFlag {
        NO(0, "不置顶"),
        YES(1, "置顶");
        @Getter
        private final Integer code;
        private final String desc;

    }


    @AllArgsConstructor
    public enum ShowStatus {
        NO(0, "不展示"),
        YES(1, "展示");
        @Getter
        private final Integer code;
        private final String desc;

    }

    @AllArgsConstructor
    public enum CheckStatus {
        WAIT(0, "待审核"),
        CHECKING(1, "审核中"),
        PASS(2, "审核通过"),
        NOT_PASS(3, "审核不通过");
        @Getter
        private final Integer code;
        private final String desc;

    }


}
