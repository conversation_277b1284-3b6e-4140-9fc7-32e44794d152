package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/10/25 17:56
 * @Description 评论类型枚举
 */
@Getter
@AllArgsConstructor
public enum CommentTypeEnum {
    EXPERIENCE(1, "心得评论"),
    COURSE(2, "课件评论"),
    ENTERPRISE(3, "成长专区评论"),
    MINI_EXPERIENCE(4, "小程序文字心得评论"),
    MINI_VIDEO(5, "小程序视频心得评论"),
    MINI_MEETING(6, "小程序会议评论"),
    MINI_GROUP_TEXT(11, "小组文字评论"),
    MINI_GROUP_VIDEO(12, "小组视频评论"),
    MINI_MEETING_QUESTION(13, "小程序会议问题"),
    FAMILY_HAPPY_SUMMER(14, "家庭幸福暑期活动"),
    GROUP_READ(15, "小组共读"),
    GROUP_LETTER(16, "小组家书"),
    GROWTH_CASE(17, "成长案例"),
    MINI_CHECK_IN(19, "小程序打卡"),
    GROUP_READ_TASK(20, "共读任务"),
    GROUP_READ_TASK_RECORD(21, "共读任务记录"),
    IDEA(22, "想法"),
    TEN_JOIN_HEART(23, "十家连心周报"),
    HEADLINES_AUDIO(24, "润泽头条音频评论"),
    PUBLIC_MEETING(25, "橐龠会议内容"),
    GROWTH_PLAN(28, "成长计划心得"),
    COURSE_NOTEPAD(29, "课程笔记"),
    COMMON_CONTENT(30, "公共内容"),
    MAIL(31, "信箱"),
    TRAILER(32, "公共小视频"),
    ;

    private Integer code;
    private String value;

    public static CommentTypeEnum getByCode(Integer code) {
        for (CommentTypeEnum value : CommentTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
