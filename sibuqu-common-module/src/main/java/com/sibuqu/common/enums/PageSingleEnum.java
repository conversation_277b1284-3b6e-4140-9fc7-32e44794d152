package com.sibuqu.common.enums;

import lombok.Getter;

public class PageSingleEnum {

    @Getter
    public enum RecommendFlag {
        NO(0, "不推荐"),
        YES(1, "推荐");
        private Integer code;
        private String desc;

        RecommendFlag(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @Getter
    public enum UpStatus {
        DOWN(0, "下架"),
        UP(1, "上架");
        private Integer code;
        private String desc;

        UpStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @Getter
    public enum PageType {
        ALL(0, "全部"),
        HOME_PAGE(1, "首页"),
        COLUMN_PAGE(2, "栏目页面"),
        CONTENT_PAGE(3, "内容页面"),
        INDEPENDENT_PAGE(4, "独立页面");
        private Integer code;
        private String desc;

        PageType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @Getter
    public enum JumpType {
        PAGE(1, "页面类型"),
        ADDRESS(2, "地址"),
        COURSE(3, "课程"),
        LIVE(4, "独立直播"),
        PICTURE(5, "图片"),
        PAYMENT(6, "支付页面");
        private Integer code;
        private String desc;

        JumpType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
}
