package com.sibuqu.common.enums;

import lombok.Getter;

public class CommentRecordEnum {

    @Getter
    public enum CommentBOType {
        TEXT(1, "文本"),
        VOICE(2, "语音"),
        IMAGE(3, "图片");

        final Integer code;
        final String desc;

        CommentBOType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }

    @Getter
    public enum DataFlagEnum {
        HIDE(0, "隐藏"),
        SHOW(1, "显示");

        final Integer code;
        final String desc;

        DataFlagEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

}
