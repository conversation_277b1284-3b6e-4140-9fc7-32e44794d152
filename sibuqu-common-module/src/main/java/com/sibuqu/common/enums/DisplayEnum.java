package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @Date 2022/8/25 15:23
 * @Version 1.0
 * @Description 是否显示
 **/
public enum DisplayEnum {
    /**
     * 隐藏
     */
    NO(0,"隐藏"),
    /**
     *显示
     */
    YES(1,"显示");

    private Integer code;
    private String value;

    private DisplayEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        // TODO Auto-generated method stub
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    public static DisplayEnum get(Integer code) {
        for (DisplayEnum model : EnumSet.allOf(DisplayEnum.class)) {
            if (model.code==code.intValue()) {
                return model;
            }
        }
        return null;
    }

    public static DisplayEnum get(String value){
        for (DisplayEnum model : EnumSet.allOf(DisplayEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
    }
}
