package com.sibuqu.common.enums;

import lombok.Getter;

public class ExportFileEnum {

    @Getter
    public enum DataTypeEnum {
        STUDY_PARTY_LIVE_POINT_ENROLL_DATA(1, "学习会直播点报名数据"),
        STUDY_PARTY_LIVE_POINT_DATA(2, "学习会直播点数据");

        private Integer code;
        private String value;

        DataTypeEnum(Integer code, String value) {
            this.code = code;
            this.value = value;
        }

    }

    @Getter
    public enum ExportStatusEnum {
        NOT_STARTED(1, "未开始"),
        EXECUTING(2, "执行中"),
        EXECUTED_SUCCESSFULLY(3, "执行成功"),
        EXECUTION_FAILED(4, "执行失败");

        private Integer code;
        private String value;

        ExportStatusEnum(Integer code, String value) {
            this.code = code;
            this.value = value;
        }

    }
}
