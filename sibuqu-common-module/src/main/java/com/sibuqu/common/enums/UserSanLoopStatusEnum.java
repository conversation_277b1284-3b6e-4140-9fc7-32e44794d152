package com.sibuqu.common.enums;

/**
 * @ClassName UserSanLoopStatusEnum.java
 * <AUTHOR>
 * @Version 1.0.0
 * @Description 服务号 轮询状态枚举
 * @CreateTime 2024年04月17日 10:49:56
 */
public enum UserSanLoopStatusEnum {
    /**
  /*   * 已过期
     *//*
    EXPIRED(1),
    *//**
     * 继续轮询
     *//*
    LOOP(2),
    *//**
     * 已注册
     *//*
    REG(3),
    *//**
     * 未注册
     *//*
    NOT_REG(4),
    // 开始轮询
    public WeixinUserStatusResponseVO;

    userStatus(WeixinMPRequestVO req) {
        String uuid = req.getUuid();
        String source = req.getSource();
        String openId = (String) redisUtil.get(WEIXIN_MP_USER_OPENID.getKey(uuid));
        WeixinUserStatusResponseVO weixinUserStatusResponseVO = new WeixinUserStatusResponseVO();
        Object value = redisUtil.get(WEIXIN_MP_USER_STATUS.getKey(uuid));
        if (value == null) {
            return weixinUserStatusResponseVO.setStatus(UserSanLoopStatusEnum.EXPIRED.getType());
        }
        Integer status = (Integer) value;
        // 如果用户扫码是已注册，就直接根据openId获取用户token
        if (status.equals(UserSanLoopStatusEnum.REG.getType())) {
            String token = getToken(openId);
            weixinUserStatusResponseVO.setToken(token);
        }
        return weixinUserStatusResponseVO.setStatus(status);
    }

    UserSanLoopStatusEnum(int i) {
    }*/
}

