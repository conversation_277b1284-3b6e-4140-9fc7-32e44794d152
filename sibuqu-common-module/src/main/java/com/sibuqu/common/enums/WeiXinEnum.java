package com.sibuqu.common.enums;

public class WeiXinEnum {

    // 场景枚举值（1 资料；2 评论；3 论坛；4 社交日志）
    public enum SceneEnum {
        DATA(1, "资料"),
        COMMENT(2, "评论"),
        FORUM(3, "论坛"),
        SOCIAL_LOG(4, "社交日志");

        private Integer code;
        private String desc;

        SceneEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    // mediaType 1:音频;2:图片;3:视频
    public enum MediaTypeEnum {
        AUDIO(1, "音频"),
        IMAGE(2, "图片"),
        VIDEO(3, "视频");

        private Integer code;
        private String desc;

        MediaTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
