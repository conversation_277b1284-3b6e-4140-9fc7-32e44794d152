package com.sibuqu.common.enums;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @Description 公共字典表  字典类型（1课程编码 2企业行业 3热搜关键词 4帮助中心地址 5企业标签 6 职位）',
 * @CreateTime 2022年12月06日 19:55:00
 */
public enum DictTypeEnum {

    COURSE_CODE(1, "课程编码"),
    COMPANY_INDUSTRY(2, "企业行业"),
    HOT_SEARCH_KEYWORDS(3, "热搜关键词"),
    HELP_ADDRESS(4, "帮助中心地址"),
    COMPANY_LABEL(5, "企业标签"),
    POSITION(6, "职位"),
    AGE(10, "年龄"),
    GENDER(11, "性别"),
    UNIFORM_POSITION(12, "一致化职位"),
    COMPANY_EMPLOYEE_NUMBER(13, "企业员工数"),
    TURNOVER(14, "营业额"),
    STAGES_OF_LIFE(15, "人生阶段"),
    THE_ONLY_CHILD(16, "是否为独生子"),
    HAVING_BABY(17, "是否有宝宝"),
    MARITAL(18, "婚姻状况"),
    EDUCATION(19, "学历"),
    EMPLOYMENT(20, "在职时长"),
    TEAM(21, "团队人数"),
    INTEREST_LABEL(22, "兴趣标签"),
    ;
    private final Integer type;
    private final String desc;

    DictTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
