package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/10/25 17:56
 * @Description 点赞类型枚举
 */
@Getter
@AllArgsConstructor
public enum LikeTypeEnum {
    // 点赞类型 1-心得点赞 2-课件评论点赞 3-课件资源点赞 4-成长专区点赞 5-小程序心得点赞 6-小程序视频点赞 7-小程序会议点赞 11-小组文字点赞 12-小组视频点赞 13-小程序会议问题 14-家庭幸福暑期活动 15-小组共读 16-家书 17-成长案例 18-知行卡践行 19-小程序打卡 20-共读任务 21-共读任务记录 22-想法 23-十家连心周报 24-润泽头条音频点赞 25-橐龠会议内容 26-企业主页 27-用户学习排行榜 28-成长计划心得
    HEART_LIKE(1, "心得点赞"),
    COURSEWARE_COMMENT_LIKE(2, "课件评论点赞"),
    COURSEWARE_RESOURCE_LIKE(3, "课件资源点赞"),
    ENTERPRISE(4, "成长专区点赞"),
    MINI_EXPERIENCE(5, "小程序文字心得点赞"),
    MINI_VIDEO(6, "小程序视频心得点赞"),
    MINI_MEETING(7, "小程序会议点赞"),
    MINI_GROUP_TEXT(11, "小组文字点赞"),
    MINI_GROUP_VIDEO(12, "小组视频点赞"),
    MINI_MEETING_QUESTION(13, "小程序会议问题"),
    FAMILY_HAPPY_SUMMER(14, "家庭幸福暑期活动"),
    GROUP_READ(15, "小组共读"),
    GROUP_LETTER(16, "小组家书"),
    GROWTH_CASE(17, "成长案例"),
    KNOWLEDGE_RUN_LIKE(18, "知行卡践行"),
    MINI_CHECKIN(19, "小程序打卡点赞"),
    GROUP_READ_TASK(20, "共读任务"),
    GROUP_READ_TASK_RECORD(21, "共读任务记录"),
    IDEA(22, "想法"),
    TEN_JOIN_HEART(23, "十家连心周报"),
    HEADLINES_AUDIO(24, "润泽头条音频点赞"),
    PUBLIC_MEETING(25, "橐龠会议内容"),
    COMPANY_HOME(26, "企业主页"),
    USER_STUDY_RANKING(27, "用户学习排行榜"),
    GROWTH_PLAN(28, "成长计划心得"),
    COURSE_NOTEPAD(29, "课程笔记"),
    COMMON_CONTENT(30, "公共内容"),
    MAIL(31, "信箱"),
    TRAILER(32, "公共小视频"),
    COMMENT(33, "评论"),
    REPLY(34, "回复"),
    ;

    private final Integer code;
    private final String value;

    public static LikeTypeEnum getByCode(Integer code) {
        for (LikeTypeEnum value : LikeTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
