package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @date  2021-06-17 10:45
 * 发布类型0立即发布1定时发布2不发布
 */
public enum PublishTypeEnum {
	/**
	 * 立即发布
	 */
	PUBLISH_NOW(0,"立即发布"),
	/**
	 * 定时发布
	 */
	TIMED_PUBLISH(1,"定时发布"),
	/**
	 * 不发布
	 */
	NO_PUBLISH(2,"不发布"),
	;

	private Integer code;
	private String value;

	PublishTypeEnum(Integer code, String value) {
		this.code=code;
		this.value=value;
	}

	public Integer getCode() {
		// TODO Auto-generated method stub
		return this.code;
	}

	public String getValue() {
		return this.value;
	}

	public static PublishTypeEnum get(Integer code) {
		for (PublishTypeEnum model : EnumSet.allOf(PublishTypeEnum.class)) {
			if (model.code==code.intValue()) {
				return model;
			}
		}
		return null;
	}

	public static PublishTypeEnum get(String value){
		for (PublishTypeEnum model : EnumSet.allOf(PublishTypeEnum.class)) {
			if (model.value.equals(value)) {
				return model;
			}
		}
		return null;
	}

}
