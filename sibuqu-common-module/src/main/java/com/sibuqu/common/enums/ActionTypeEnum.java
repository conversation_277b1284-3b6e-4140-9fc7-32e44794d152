package com.sibuqu.common.enums;

import java.util.EnumSet;

/**
 * <AUTHOR>
 * @date  2021-06-17 10:45
 * 操作类型0不跳转1H5页面2商品3课件
 */
public enum ActionTypeEnum {

	DONT_JUMP(0,"不跳转"),
	H5(1,"h5页面"),
	SKU(2,"商品"),
	COURSE(3,"课件"),
	LIVE(5,"独立直播"),
	ACTIVITY_GROUP(6,"学习小组主页"),

	;

	private Integer code;
    private String value;

    ActionTypeEnum(Integer code, String value) {
		this.code=code;
		this.value=value;
	}

	public Integer getCode() {
		// TODO Auto-generated method stub
		return this.code;
	}

	public String getValue() {
		return this.value;
	}

	public static ActionTypeEnum get(Integer code) {
		for (ActionTypeEnum model : EnumSet.allOf(ActionTypeEnum.class)) {
			if (model.code==code.intValue()) {
				return model;
			}
		}
		return null;
	}

	public static ActionTypeEnum get(String value){
		for (ActionTypeEnum model : EnumSet.allOf(ActionTypeEnum.class)) {
            if (model.value.equals(value)) {
                return model;
            }
        }
        return null;
	}

}
