package com.sibuqu.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/10/25 17:56
 * @Description 意见反馈类型枚举
 */
@Getter
@AllArgsConstructor
public enum FeedBackTypeEnum {

    USER_FEEDBACK(0, "用户反馈"),

    TEACHER_FEEDBACK(1, "老师反馈");

    private Integer code;
    private String value;

    public static FeedBackTypeEnum getByCode(Integer code) {
        for (FeedBackTypeEnum value : FeedBackTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
