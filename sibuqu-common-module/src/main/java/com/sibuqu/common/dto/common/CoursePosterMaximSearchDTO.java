package com.sibuqu.common.dto.common;

import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2021/10/27 8:01
 * @Description 课程分享的 海报箴言 搜索
 */
@Data
@Builder
public class CoursePosterMaximSearchDTO extends PageBase {

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty(value = "课程ID", required = true, dataType = "Integer")
    private Integer courseId;

    @ApiModelProperty(value = "出处", required = true, dataType = "String")
    private String source;

    @ApiModelProperty(value = "海报箴言", required = true, dataType = "String")
    private String maxim;

    @ApiModelProperty(value = "箴言类型（1.海报箴言  2.经典撞击 3.天地常新金句 4.幸福家庭暑期活动金句 5.向阳明先生请教 6.战略app图片金句）", required = true, dataType = "Integer")
    @NotNull(message = "箴言类型不能为空")
    private Integer type;

}
