package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/18-4:19 下午
 * 用于远程调用 针对推送详情中的服务 查出不同的用户信息
 */
@Data
public class MessageInfoSkuOrResDTO {
    @ApiModelProperty("跳转类型中relId")
    private Integer jumpRelId;
    @ApiModelProperty("跳转类型中课件的id")
    private Integer jumpResId;

    @ApiModelProperty("跳转类型中推送跳转的多个商品的id")
    private List<Integer> jumpSkuIds;
    @ApiModelProperty("发送对象中推送自定义的多个商品的id")
    private List<Integer> customizeSkuIds;

}
