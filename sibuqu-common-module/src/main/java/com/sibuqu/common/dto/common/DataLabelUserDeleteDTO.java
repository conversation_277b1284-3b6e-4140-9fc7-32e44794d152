package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 数据标签人员删除
 * @createTime 2022年12月14日17:52:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataLabelUserDeleteDTO implements Serializable {

    @ApiModelProperty(value = "企业ID",required = true)
    @NotNull(message = "企业ID不能为空")
    private Integer companyId;

    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不能为空")
    private Integer userId;

}
