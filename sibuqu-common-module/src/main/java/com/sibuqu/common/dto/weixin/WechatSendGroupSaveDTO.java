package com.sibuqu.common.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 微信下模板消息的发送群组
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Data
@Accessors(chain = true)
@ApiModel( description = "微信下模板消息的发送群组")
public class WechatSendGroupSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

//    @ApiModelProperty("主键")
//    private Integer id;

    @ApiModelProperty(value = "消息模板id",required = true)
    @NotNull(message = "消息模板id不能为空!")
    private Integer msgTempId;

    @ApiModelProperty(value = "发送对象 1:数据标签 2:课程",required = true)
    @NotNull(message = "发送对象不能为空!")
    private Integer sendObject;

    @ApiModelProperty(value = "发送类型下的标签或课程不能为空",required = true)
    @NotEmpty(message = "类型下的列表不能为空!")
    private List<DataProgram> dataList;


    @Data
    public static class DataProgram implements Serializable {
        private static final long serialVersionUID = -7945254706501974849L;
        @ApiModelProperty(value ="发送对象的id 数据标签id，商品id",required = true)
        private String dataId;

        @ApiModelProperty(value ="发送对象的名称  数据标签名称，商品名称",required = true)
        private String dataName;
    }
}
