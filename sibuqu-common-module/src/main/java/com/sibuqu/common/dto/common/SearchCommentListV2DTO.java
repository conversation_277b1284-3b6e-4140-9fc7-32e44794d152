package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class SearchCommentListV2DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "数据ID,心得评论对应作业id,课件资源点赞对应课件id")
    @NotNull(message = "数据ID不能为空")
    private Integer dataId;

    @ApiModelProperty(value = "评论类型 1-心得评论 2-课件评论 3-成长专区评论 4-小程序心得评论 5-小程序视频评论 6-小程序会议评论 11-小组文字评论 12-小组视频评论 13-小程序会议问题 14-家庭幸福暑期活动 15-小组共读 16-家书 17-成长案例 19-小程序打卡 20-共读任务 21-共读任务记录 22-想法 23-十家连心周报 25-橐龠会议内容 28-成长计划心得 29-课程笔记 30-公共内容 31-信箱")
    @NotNull(message = "评论类型不能为空")
    private Integer commentType;

    @ApiModelProperty(value = "是否包含回复内容 1-包含 0-不包含")
    private Integer containReply;

    @ApiModelProperty(value = "页码")
    private int pageNum = 1;

    @ApiModelProperty("分页大小")
    private int pageSize = 10;

    @ApiModelProperty(value = "平台类型（0.企业版 1.个人版）",notes = "平台类型（0.企业版 1.个人版）",required = true)
    private Integer platformType;

    @ApiModelProperty(value="企业 id")
    private Integer companyId;

}
