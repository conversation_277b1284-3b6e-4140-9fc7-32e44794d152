package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LikeStatusByDataIdListAndUserIdDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户ID列表")
    private Integer userId;

    @ApiModelProperty("数据ID")
    private List<Integer> dataIdList;
}
