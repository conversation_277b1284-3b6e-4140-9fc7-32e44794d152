package com.sibuqu.common.dto.common;

import com.sibuqu.common.bo.CommentBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class InnerDeleteCommentV2DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    @NotNull(message = "用户id不能为空")
    private Integer userId;

    @NotNull(message = "评论类型不能为空")
    @NotNull(message = "评论类型不能为空")
    private Integer commentType;

    @NotNull(message = "数据ID不能为空")
    @NotNull(message = "数据ID不能为空")
    private Integer dataId;

    @ApiModelProperty(value = "原始内容id")
    @NotNull(message = "原始内容id不能为空")
    private Long originContentId;

}
