package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/5/10-11:23
 * 直播老师回复 图片 文本 列表信息
 */

@Data
public class LiveTeacherReplyIdDTO {
    /**
     * 主键信息
     */
    @ApiModelProperty(value = "主键信息",required = true,dataType = "Integer")
    @NotNull
    private Integer id;
}
