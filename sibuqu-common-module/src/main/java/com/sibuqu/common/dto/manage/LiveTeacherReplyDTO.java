package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/10-11:23
 * 直播老师回复 图片 文本 列表信息
 */

@Data
public class LiveTeacherReplyDTO {
    /**
     * 主键信息
     */
    @ApiModelProperty(value = "主键信息",required = false,dataType = "Integer")
    private Integer id;
    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id",required = false,dataType = "Integer")
    private Integer courseId;
/*    *//**
     * 资源id 就是直播的主键 roomId
     *//*
    @ApiModelProperty(value = "资源id 就是直播的主键 roomId",required = false,dataType = "Integer")
    private Integer resId;
    *//**
     * 课程与资源的中间表id
     *//*
    @ApiModelProperty(value = "课程与资源的中间表id",required = false,dataType = "Integer")
    private Integer relId;*/
    @ApiModelProperty(value = "资源id")
    private Integer resourceId;

    @ApiModelProperty("关联课程时间表ID")
    private Integer courseTimetableId;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id",required = false,dataType = "Integer")
    private Integer userId;
    /**
     * 消息类型，0：文本 1.图片 3.图片和文字
     */
    @ApiModelProperty(value = "消息类型，0：文本 1.图片 3.图片和文字 4.公告消息",required = false,dataType = "Integer")
    private Integer replyType;
    /**
     * 直播回复的内容
     */
    @ApiModelProperty(value = "直播回复的内容",required = false,dataType = "String")
    private String postContent;
    /**
     * 是否已发送 0：否 1：是
     */
    @ApiModelProperty(value = "是否已发送 0:否 1:是",required = false,dataType = "Integer")
    private Integer isSend;
    /**
     * 消息类型是 图片消息，图片的地址
     */
    @ApiModelProperty(value = "消息类型是 图片消息，图片的地址",required = false,dataType = "String")
    private String imageUrl;
}
