package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 数据标签移除用户DTO
 * @createTime 2021年10月13日 16:13:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataLabelUserRemoveDTO implements Serializable {

    @ApiModelProperty("标签ID")
    @NotNull(message = "标签ID不能为空")
    private Integer dataLabelId;

    @ApiModelProperty("用户ID")
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

}
