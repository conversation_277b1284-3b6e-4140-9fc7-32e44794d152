package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class VisibleRangeCourseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("课程 id")
    private Integer courseId;

    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("用户角色 1-全部 2-班主任")
    private Integer userRole;

}
