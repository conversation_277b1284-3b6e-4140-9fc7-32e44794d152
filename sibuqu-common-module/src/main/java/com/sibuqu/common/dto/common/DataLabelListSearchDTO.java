package com.sibuqu.common.dto.common;

import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 内部服务数据标签查询DTO
 * @createTime 2022年5月31日14:22:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataLabelListSearchDTO implements Serializable {

    @ApiModelProperty("企业ID")
    private Integer companyId;

    @ApiModelProperty("数据标签名称")
    private String labelName;

}
