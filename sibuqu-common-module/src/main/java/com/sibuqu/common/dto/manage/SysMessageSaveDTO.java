package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @since 2021-06-17 09:44:00
 * 推送消息管理中的消息添加
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SysMessageSaveDTO implements Serializable {
    private static final long serialVersionUID = 771931919112795180L;
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键",required = false,dataType = "Integer")
    private Integer id;
    /**
    * 消息标题
    */
    @ApiModelProperty(value = "消息标题",required = false,dataType = "String")
    private String title;
    /**
    * 消息内容
    */
    @ApiModelProperty(value = "消息内容",required = false,dataType = "String")
    private String content;
    /**
    * 消息类型1推送2短信3站内信
    */
//    @ApiModelProperty(value = "消息类型1推送2短信3站内信",required = false,dataType = "Integer")
//    private Integer type;
    /**
     * 列表图地址
     */
    @ApiModelProperty(value = "列表图地址",required = false,dataType = "String")
    private String imageUrl;
    /**
    * 字典表1级
    */
    @ApiModelProperty(value = "字典表1级",required = false,dataType = "String")
    private String dictionaryOne;
    /**
    * 字典表2级
    */
    @ApiModelProperty(value = "字典表2级",required = false,dataType = "String")
    private String dictionaryTwo;
    /**
    * 字典表3级
    */
    @ApiModelProperty(value = "字典表3级",required = false,dataType = "String")
    private String dictionaryThree;
    /**
     * 导入文件地址 resource  source
     */
    @ApiModelProperty(value = "导入文件地址",required = false,dataType = "String")
    private String resource;
    /**
    * 导入用户批次号
    */
    @ApiModelProperty(value = "导入用户批次号",required = false,dataType = "String")
    private String batchId;
    /**
    * 商品ids
    */
    @ApiModelProperty(value = "商品ids",required = false,dataType = "String")
    private String skuIds;
    /**
    * 操作类型0不跳转1H5页面2商品3课件
    */
    @ApiModelProperty(value = "操作类型0不跳转1H5页面2商品3课件",required = false,dataType = "Integer")
    private Integer actionType;
    /**
    * 跳转的具体信息，H5页面存地址，商品存商品ID(sku_id)，课件存课件rel_id
    */
    @ApiModelProperty(value = "跳转的具体信息，H5页面存地址，商品存商品ID(sku_id)，课件存课件rel_id",required = false,dataType = "String")
    private String actionInfo;
    /**
    * 发布类型0立即发布1定时发布2暂不发布
    */
    @ApiModelProperty(value = "发布类型0立即发布1定时发布2暂不发布",required = false,dataType = "Integer")
    private Integer publishType;
    /**
    * 发布时间
    */
    @ApiModelProperty(value = "发布时间",required = false,dataType = "String")
    private String publishTime;
}