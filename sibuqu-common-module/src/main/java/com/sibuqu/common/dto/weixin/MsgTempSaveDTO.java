package com.sibuqu.common.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 微信下的消息模板消息 保存
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "CommonWechatMsgTemp对象", description = "微信下的消息模板消息")
public class MsgTempSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty(value = "推送主体标题",required = true)
    @NotNull(message = "推送主体不能为空!")
    private String title;

    @ApiModelProperty("服务号的模板id")
    private String serviceNumberTemplateId;


    @ApiModelProperty(value = "服务号模板下的具体发送data模板内容",required = true)
    @NotEmpty(message = "模板内容不能为空!")
    private List<WxTemplateDataJsonDTO> templateDataList;


    @ApiModelProperty("推送时间")
    private LocalDateTime sendDate;

    @ApiModelProperty("发布状态: 0:未开启 1:已完成 2：已关闭")
    private Integer publishStatus;

    @ApiModelProperty("发送类型  0:手动 1:自动")
    private Integer sendType;

    @ApiModelProperty("模板跳转类型 1:H5页面链接 2:小程序链接 3:无跳转")
    private Integer tempJumpType;

    @ApiModelProperty("链接地址")
    private String linkAddress;

    @ApiModelProperty("模板小程序下的appid")
    private String miniAppId;

    @ApiModelProperty("具体发送模板的json信息")
    private String messageTemplateJson;

    @ApiModelProperty(value ="服务号模板的内容",required = true)
    @NotNull(message = "服务号模板的内容不能为空!")
    private String templateContent;

    @ApiModelProperty(value ="服务号模板的内容案例",required = false)
    private String templateExample;

    @ApiModelProperty(value ="服务号模板的标题",required = true)
    //@NotNull(message = "服务号模板的标题不能为空!")

    private String templateTitle;

}
