package com.sibuqu.common.dto.content;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CheckUserSubmitWeekWorkDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("课程 id")
    private Integer courseId;
    
    @ApiModelProperty("周作业 id 列表")
    @NotEmpty(message = "周作业 id 列表不能为空")
    private List<Long> weekWorkIdList;
    
    @ApiModelProperty("用户 id 列表")
    @NotEmpty(message = "用户 id 列表不能为空")
    private List<Integer> userIdList;

}