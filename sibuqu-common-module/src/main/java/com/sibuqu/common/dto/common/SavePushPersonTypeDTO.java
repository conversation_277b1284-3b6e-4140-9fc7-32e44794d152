package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class SavePushPersonTypeDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "推送人员类型 1:全部用户 2:按照课程权限 3:指定用户")
    @NotNull(message = "推送人员类型不能为空")
    private Integer pushPersonType;

    @ApiModelProperty(value = "推送用户权限列表")
    private List<PushPersonSourceDTO> pushPersonSourceList;

    @ApiModelProperty(value = "文件上传地址数组")
    private List<String> fileUrl;

    @ApiModelProperty(value = "app来源 1:润泽园app 2:战略创新app")
    private Integer source;
}
