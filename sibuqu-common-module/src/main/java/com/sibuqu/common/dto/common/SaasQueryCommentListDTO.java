package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class SaasQueryCommentListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "第几页")
    private int pageNum;

    @ApiModelProperty("分页大小")
    private int pageSize;

    @ApiModelProperty("课程 id")
    @NotNull(message = "课程 id 不能为空")
    private Integer courseId;

    @ApiModelProperty("课件 id")
    private Integer courseTimetableId;

    @ApiModelProperty("评论用户名称")
    private String userName;

    @ApiModelProperty("评论用户手机号")
    private String userPhone;

    @ApiModelProperty("评论开始时间")
    private LocalDateTime beginTime;

    @ApiModelProperty("评论结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("企业id")
    @NotNull(message = "企业 id不能为空")
    private Integer companyId;

}
