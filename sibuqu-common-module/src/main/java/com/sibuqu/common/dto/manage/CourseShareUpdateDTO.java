package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/22 14:20
 * @Description 课程分享配置修改
 */
@Data
public class CourseShareUpdateDTO implements Serializable {

    @ApiModelProperty(value = "课程Id", required = true, dataType = "Integer")
    @NotNull(message = "课程Id不能为空")
    private Integer courseId;

    @ApiModelProperty(value = "企业ID（个人版传 -1）", required = true, dataType = "Integer")
    @NotNull(message = "企业ID不能为空")
    private Integer companyId;

    @ApiModelProperty(value = "主键", required = false, dataType = "Integer")
    private Integer id;

    @ApiModelProperty(value = "分享语", required = false, dataType = "String")
    private String shareWords;

    @ApiModelProperty(value = "标题", required = false, dataType = "String")
    private String title;

    @ApiModelProperty(value = "描述", required = false, dataType = "String")
    private String descInfo;

    @ApiModelProperty(value = "分享链接-分享图地址", required = false, dataType = "String")
    private String shareMapUrl;

    @ApiModelProperty(value = "海报图地址", required = false, dataType = "String")
    private String posterUrl;

    @ApiModelProperty(value = "课程详情链接", required = false, dataType = "String")
    private String courseDetailsUrl;

    @ApiModelProperty(value = "课程详情二维码", required = false, dataType = "String")
    private String courseQrCodeUrl;

    @ApiModelProperty(value = "加入班级的分享文字描述",required = false,dataType = "String")
    private String joinClassesDesc;

    @ApiModelProperty(value = "加入班级的分享的背景图颜色色值",required = false,dataType = "String")
    private String joinClassesBgcolor;

    @ApiModelProperty(value = "加入班级的分享的图片",required = false,dataType = "String")
    private String joinClassesImage;

    @ApiModelProperty(value = "加入班级的分享的海报图",required = false,dataType = "String")
    private String joinClassesPoster;

    @ApiModelProperty(value = "操作人",required = false,dataType = "Integer")
    private Integer optUserId;

    @ApiModelProperty(value="课程分享-海报导语")
    private String courseShareIntroduction;

    @ApiModelProperty(value="班级分享-标题")
    private String shareClassesTitle;

    @ApiModelProperty(value="班级分享-内容")
    private String shareClassesDesc;

    @ApiModelProperty(value="班级分享-封面图")
    private String shareClassesImage;

}