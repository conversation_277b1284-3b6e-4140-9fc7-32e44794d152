package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2022/08/25 17:41
 * @Version 1.0
 * @Description 重点信息--直播的id
 **/
@Data
public class LiveAnswerLiveDetailDTO {
    @ApiModelProperty(value = "课程表id",required = true)
    @NotNull(message = "课程表id不能为空")
    private Integer  courseTimetableId;

}
