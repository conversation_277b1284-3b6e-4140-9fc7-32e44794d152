package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2021/10/22 12:05
 * @Description 个人资料修改账号信息
 */
@Data
public class PersonalUserUpdateDTO {
    @ApiModelProperty(value = "主键", required = true, dataType = "Integer")
    @NotNull(message = "主键不能为空")
    private Integer id;
    /**
     * 原密码
     */
    @ApiModelProperty(value = "原密码", required = true, dataType = "String")
    @NotNull(message = "原密码不能为空")
    private String password;
    /**
     * 新密码
     */
    @ApiModelProperty(value = "新密码", required = true, dataType = "String")
    @NotNull(message = "新密码不能为空")
    private String newPassword;
    /**
     * 确认密码
     */
    @ApiModelProperty(value = "确认密码", required = true, dataType = "String")
    @NotNull(message = "确认密码不能为空")
    private String confirmPassword;





}
