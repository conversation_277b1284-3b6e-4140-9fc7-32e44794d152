package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/22 10:44
 * @Description 添加系统用户参数
 */
@Data
public class SysUserAddDTO {

    @ApiModelProperty(value ="用户姓名", required = true, dataType = "String")
    @NotNull(message = "用户姓名不能为空")
    private String personName;

    @ApiModelProperty(value ="密码", required = true, dataType = "String")
    @NotNull(message = "密码不能为空")
    private String password;

    @ApiModelProperty(value ="手机号", required = true, dataType = "String")
    @NotNull(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value ="账号状态 0:禁用 1：启用", required = true, dataType = "String")
    private String state;

    @ApiModelProperty(value = "角色的id集合", required = false, dataType = "List<Integer>")
    private List<Integer> roleIds;
}
