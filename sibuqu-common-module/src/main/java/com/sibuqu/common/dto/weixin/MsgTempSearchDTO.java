package com.sibuqu.common.dto.weixin;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/05/11-11:13
 * 服务号模板消息列表
 */

@Data
public class MsgTempSearchDTO extends PageBase {

    @ApiModelProperty("服务号的模板id")
    private String serviceNumberTemplateId;

    @ApiModelProperty("消息标题")
    private String title;

    @ApiModelProperty("发布状态: 0:未开启 1:已完成 2：已关闭")
    private Integer publishStatus;
}
