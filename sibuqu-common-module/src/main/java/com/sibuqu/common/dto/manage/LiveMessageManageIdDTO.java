package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/8/26-16:16
 * 直播消息管理 图片 文本 列表信息
 */

@Data
public class LiveMessageManageIdDTO {
    /**
     * 主键信息
     */
    @ApiModelProperty(value = "主键信息",required = true,dataType = "Integer")
    @NotNull
    private Integer id;
}
