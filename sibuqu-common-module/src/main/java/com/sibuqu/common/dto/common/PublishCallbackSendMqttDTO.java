package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PublishCallbackSendMqttDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("课件 id")
    private Integer courseTimetableId;

    @ApiModelProperty("事件 publish-推流 publish_done-断流")
    private String action;

}
