package com.sibuqu.common.dto.weixin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PostCallbackDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    //ToUserName	string	小程序的username
    //FromUserName	string	平台推送服务UserName
    //CreateTime	number	发送时间
    //MsgType	string	默认为：event
    //Event	string	默认为：wxa_media_check
    //appid	string	小程序的appid
    //trace_id	string	任务id
    //version	number	可用于区分接口版本
    //result	object	综合结果
    //detail	array	详细检测结果

    @ApiModelProperty(value = "小程序的username")
    private String ToUserName;

    @ApiModelProperty(value = "平台推送服务UserName")
    private String FromUserName;

    @ApiModelProperty(value = "发送时间")
    private Long CreateTime;

    @ApiModelProperty(value = "默认为：event")
    private String MsgType;

    @ApiModelProperty(value = "默认为：wxa_media_check")
    private String Event;

    @ApiModelProperty(value = "小程序的appid")
    private String appid;

    @ApiModelProperty(value = "任务id")
    private String trace_id;

    @ApiModelProperty(value = "可用于区分接口版本")
    private Integer version;

    @ApiModelProperty(value = "综合结果")
    private Result result;

    @ApiModelProperty(value = "详细检测结果")
    private List<Detail> detail;

    @ApiModelProperty(value = "错误码，仅当该值为0时，该项结果有效")
    private Integer errcode;

    @ApiModelProperty(value = "错误信息，仅当该值为0时，该项结果有效")
    private String errmsg;

    @Data
    public static class Result {
        //suggest	string	建议，有risky、pass、review三种值
        //label	number	命中标签枚举值，100 正常；20001 时政；20002 色情；20006 违法犯罪；21000 其他

        @ApiModelProperty(value = "建议，有risky、pass、review三种值")
        private String suggest;

        @ApiModelProperty(value = "命中标签枚举值，100 正常；20001 时政；20002 色情；20006 违法犯罪；21000 其他")
        private Integer label;

    }

    @Data
    public static class Detail {
        //strategy	string	策略类型
        //errcode	number	错误码，仅当该值为0时，该项结果有效
        //suggest	string	建议，有risky、pass、review三种值
        //label	number	命中标签枚举值，100 正常；20001 时政；20002 色情；20006 违法犯罪；21000 其他
        //prob	number	0-100，代表置信度，越高代表越有可能属于当前返回的标签（label）

        @ApiModelProperty(value = "策略类型")
        private String strategy;

        @ApiModelProperty(value = "错误码，仅当该值为0时，该项结果有效")
        private Integer errcode;

        @ApiModelProperty(value = "建议，有risky、pass、review三种值")
        private String suggest;

        @ApiModelProperty(value = "命中标签枚举值，100 正常；20001 时政；20002 色情；20006 违法犯罪；21000 其他")
        private Integer label;

        @ApiModelProperty(value = "0-100，代表置信度，越高代表越有可能属于当前返回的标签（label）")
        private Integer prob;

    }
}
