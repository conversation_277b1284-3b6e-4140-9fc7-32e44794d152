package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName SensitiveWordsDeleteDTO.java
 * <AUTHOR>
 * @Version 1.0.0
 * @Description 关键词删除
 * @CreateTime 2022年11月29日 14:26:20
 */
@Data
public class SensitiveWordsDeleteDTO implements Serializable {

    @ApiModelProperty(value = "主键",required = true)
    @NotNull(message = "主键不能为空")
    private Integer id;

}
