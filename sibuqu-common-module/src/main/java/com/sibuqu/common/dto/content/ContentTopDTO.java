package com.sibuqu.common.dto.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ContentTopDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "主键不能为空")
    private Long id;

    @ApiModelProperty(value = "置顶标识 0-不置顶 1-置顶")
    @NotNull(message = "置顶标识不能为空")
    private Integer topFlag;

}