package com.sibuqu.common.dto.weixin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Map;

/**
 * 订阅消息
 * @ClassName SubscribeMessage.java
 * <AUTHOR>
 * @Version 1.0.0
 * @Description TODO
 * @CreateTime 2023年06月16日 14:33:17
 */
@Data
public class SubscribeMessage   implements Serializable {
    //接口调用凭证
    //private String access_token;
    //接收者（用户）的 openid
    @ApiModelProperty(value = "接收者（用户）的 openid",required = true)
    private String touser;
    //所需下发的订阅模板id
    @ApiModelProperty(value = "订阅模板id",required = true)
    @NotBlank(message = "订阅模板id不能为空")
    private String template_id;
    //点击模板卡片后的跳转页面，仅限本小程序内的页面。支持带参数,（示例index?foo=bar）。该字段不填则模板无跳转。
    @ApiModelProperty(value = "页面 page，例如 pages/index/index，",required = true)
    @NotBlank(message = "页面page不能为空")
    private String page;
    //模板内容，格式形如 { "key1": { "value": any }, "key2": { "value": any } }
    @ApiModelProperty(value = "模板内容",required = true)
    @NotEmpty(message = "模板内容不能为空")
    private Map<String, TemplateData> data;

    @ApiModelProperty(value = "跳转小程序类型：developer为开发版；trial为体验版；formal为正式版；默认为正式版",required = true)
    @NotBlank(message = "跳转小程序类型不能为空")
    private String miniprogram_state;

}
