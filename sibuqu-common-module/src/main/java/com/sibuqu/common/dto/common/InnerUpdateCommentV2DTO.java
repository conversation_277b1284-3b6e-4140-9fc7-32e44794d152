package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class InnerUpdateCommentV2DTO extends SendCommentV2DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "用户名")
    private String userFullName;

    @ApiModelProperty("手机号")
    private String userPhone;

    @ApiModelProperty(value = "原始内容id")
    private Long originContentId;

}
