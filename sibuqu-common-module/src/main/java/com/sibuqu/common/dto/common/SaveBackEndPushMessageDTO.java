package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class SaveBackEndPushMessageDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer id;
    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "发布类型0立即发布1定时发布2不发布")
    private Integer publishType;

    @ApiModelProperty(value = "发布时间")
    private LocalDateTime publishTime;

    @ApiModelProperty(value = "列表图地址")
    private String imageUrl;

    @ApiModelProperty(value = "系统消息，活动消息-操作类型0不跳转1H5页面2商品3课件")
    private Integer actionType;

    @ApiModelProperty(value = "系统消息，活动消息-跳转的具体信息，H5页面存地址，商品存商品ID(sku_id)，课件存课件rel_id")
    private String actionInfo;
    @ApiModelProperty(value = "商品名资源名")
    private String actionTitle;

}
