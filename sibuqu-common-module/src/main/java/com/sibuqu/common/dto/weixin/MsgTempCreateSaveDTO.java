package com.sibuqu.common.dto.weixin;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 微信下的消息模板消息 保存
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Data
@Accessors(chain = true)
@ApiModel( description = "微信下的创建模板消息")
public class MsgTempCreateSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "服务号的模板id",required = true)
    @NotNull(message ="服务号的模板id不能为空" )
    private String serviceNumberTemplateId;

    @ApiModelProperty(value = "服务号模板的标题",required = true)
    @NotNull(message ="服务号模板的标题不能为空" )
    private String title;

    @ApiModelProperty(value = "服务号模板的内容",required = true)
    @NotNull(message ="服务号模板的内容不能为空" )
    private String content;

    @ApiModelProperty(value = "服务号模板的内容案例",required = true)
    @NotNull(message ="服务号模板的内容案例不能为空" )
    private String example;

}
