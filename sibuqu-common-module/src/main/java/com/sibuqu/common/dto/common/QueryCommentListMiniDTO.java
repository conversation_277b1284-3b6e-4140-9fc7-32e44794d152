package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class QueryCommentListMiniDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("评论内容")
    private String content;

    @ApiModelProperty("评论用户名称")
    private String userName;

    @ApiModelProperty("评论用户手机号")
    private String userPhone;

    @ApiModelProperty("评论时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "所属类型 1-全部 2-视频心得 3-文字心得 4-会议")
    @NotNull(message = "所属类型不能为空")
    private Integer belongType;

    @ApiModelProperty(value = "显示状态（0.隐藏 1.显示）")
    private Integer dataFlag;

    @ApiModelProperty(value = "页码")
    private int pageNum = 1;

    @ApiModelProperty("分页大小")
    private int pageSize = 10;

}
