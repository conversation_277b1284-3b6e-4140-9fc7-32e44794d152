package com.sibuqu.common.dto.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CommonContentPageListDTO {

    @ApiModelProperty("页码")
    private Integer pageNum;

    @ApiModelProperty("每页条数")
    private Integer pageSize;

    @ApiModelProperty(value = "关联数据类型 1-课程下班级内容 2-广场 3-专区 4-inno周作业")
    private Integer dataType = 1;

    @ApiModelProperty(value = "关联数据id type=1为课程id")
    private Long dataId;

    @ApiModelProperty(value = "关联子数据id type=1为班级id ")
    private Long subDataId = 0L;

    @ApiModelProperty(value = "话题id")
    private Long topicId = 0L;

    @ApiModelProperty(value = "用户id")
    private Integer userId = 0;

    @ApiModelProperty(value = "置顶标识 0-不置顶 1-置顶")
    private Integer topFlag = 0;

    @ApiModelProperty(value = "展示范围 1-公开 2-当前范围")
    private Integer showLimit;
}
