package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaasDataLabelAddManageDTO implements Serializable {

    @ApiModelProperty("企业ID")
    private Integer companyId;

    @ApiModelProperty("用户Ids")
    private List<Integer> userIds;

    @ApiModelProperty("创建人ID")
    private Integer createUserId;

    @ApiModelProperty("创建人姓名")
    private String createUserName;

}
