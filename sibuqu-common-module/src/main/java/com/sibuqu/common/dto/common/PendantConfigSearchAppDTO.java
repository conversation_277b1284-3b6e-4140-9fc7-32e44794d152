package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2021/10/27 15:28
 * @Description APP挂件配置搜索参数
 */
@Data
public class PendantConfigSearchAppDTO {

    @ApiModelProperty(value = "课程的id",required = true,dataType = "Integer")
    @NotNull(message = "课程id不能为空")
    private Integer pendantCourseId;

    @ApiModelProperty(value ="课件id(资源id)",required = true,dataType = "Integer")
    @NotNull(message = "资源id不能为空")
    private Integer pendantResId;

}
