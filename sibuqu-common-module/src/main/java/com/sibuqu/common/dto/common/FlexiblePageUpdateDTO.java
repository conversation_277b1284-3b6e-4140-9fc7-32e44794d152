package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class FlexiblePageUpdateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "主键不能为空")
    private Long id;

    @ApiModelProperty(value = "页面标题")
    @NotBlank(message = "页面标题不能为空")
    @Length(max = 20, message = "页面标题不能超过20个字符")
    private String pageTitle;

    @ApiModelProperty(value = "页面描述")
    @Length(max = 100, message = "页面描述不能超过100个字符")
    private String pageDesc;

    @ApiModelProperty(value = "页面内容")
    private String pageContent;

    @ApiModelProperty(value = "页面设置")
    private String pageSetting;

    @ApiModelProperty(value = "是否支持分享 0-不分享 1-分享")
    private Integer shareFlag;

    @ApiModelProperty(value = "分享图标")
    private String shareIcon;

    @ApiModelProperty(value = "背景图")
    private String bgUrl;

    @ApiModelProperty(value = "背景色")
    @Length(max = 7, message = "背景色不能超过7个字符")
    private String bgColor;

    @ApiModelProperty(value = "微信分享标题")
    @Length(max = 20, message = "微信分享标题不能超过20个字符")
    private String wxShareTitle;

    @ApiModelProperty(value = "微信分享描述")
    @Length(max = 100, message = "微信分享描述不能超过100个字符")
    private String wxShareDesc;

    @ApiModelProperty(value = "微信分享图标")
    private String wxShareIcon;

    @ApiModelProperty(value = "页面是否开启 0-不开启 1-开启")
    private Integer openFlag;

    @ApiModelProperty(value = "关闭页面提示")
    @Length(max = 30, message = "关闭页面提示不能超过30个字符")
    private String closePageTip;

    @ApiModelProperty(value = "页面是否需要登录 0-不需要 1-需要")
    private Integer loginFlag;

}
