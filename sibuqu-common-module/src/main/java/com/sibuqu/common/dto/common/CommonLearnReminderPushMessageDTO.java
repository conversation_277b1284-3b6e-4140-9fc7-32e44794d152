package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 学习提醒消息
 */
@Data
public class CommonLearnReminderPushMessageDTO {

    @ApiModelProperty(value = "消息接收人ID",required = true)
    private List<Integer> receiveUserIds;

    @ApiModelProperty(value = "剩余时间 剩余分钟",required = true)
    private Integer remainingMinutes;

    @ApiModelProperty(value = "商品id",required = true)
    private Integer goodsId;

    @ApiModelProperty(value = "课件名称",required = true)
    private String courseTimeTableName;

    @ApiModelProperty(value = "课程名称",required = true)
    private String courseName;
    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id",required = false,dataType = "Integer")
    private Integer courseId;

    @ApiModelProperty("课件ID")
    private Integer courseTimetableId;

}
