package com.sibuqu.common.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 微信下客服发送消息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Data
@Accessors(chain = true)
@ApiModel( description = "微信下客服发送消息")
public class WechatTextSendDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "openId",required = true)
    @NotNull(message ="openId不能为空" )
    private String openId;

    @ApiModelProperty(value = "内容",required = true)
    @NotNull(message ="内容不能为空" )
    private String content;



}
