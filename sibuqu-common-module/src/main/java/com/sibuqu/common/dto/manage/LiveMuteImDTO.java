package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/6/20-7:13 下午
 * 禁言
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LiveMuteImDTO implements Serializable {
    /**
     * 用户的userId
     */
    @ApiModelProperty(value = "用户的userId,uid",required = true,dataType = "Integer")
    @NotNull(message="用户id不能为空")
    private Integer userId;
    /**
     * 禁言类型 0 本次禁言,1全部禁言
     */
    @ApiModelProperty(value = "禁言类型 0 本次禁言(解除本次),1全部禁言(解除全部)",required = true,dataType = "Integer")
    @NotNull(message="禁言类型不能为空")
    private Integer speakType;
}
