package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/11/18 14:30
 * @Version 1.0
 * @Description 直播评论 导出的信息
 **/
@Data
public class CommentLiveExportDTO extends CommentLiveSearchDTO {
    @ApiModelProperty(value = "直播id集合",required = true,dataType = "List<String>")
    private List<String> ids;

}
