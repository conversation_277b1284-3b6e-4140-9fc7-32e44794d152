package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/25 9:48
 * @Description 互动评论回复搜索入参
 */
@Data
public class InteractCommentReplySearchDTO implements Serializable {

    @ApiModelProperty(value = "评论ID",notes = "评论ID",required = true)
    @NotNull(message = "评论ID不能为空")
    private Integer commentId;

    @ApiModelProperty(value = "页码")
    private int pageNum;

    @ApiModelProperty("分页大小")
    private int pageSize;

    private Integer userId;

    private String userName;
}
