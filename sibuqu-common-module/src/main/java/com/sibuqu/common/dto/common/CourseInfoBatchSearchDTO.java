package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 内部服务-企业课程批量查询DTO
 * @createTime 2021年10月13日 16:13:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CourseInfoBatchSearchDTO implements Serializable {

    @ApiModelProperty("企业ID")
    private Integer companyId;

    @ApiModelProperty("课程ids")
    private List<Integer> courseIds;

}
