package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2021/10/27 15:28
 * @Description H5挂件配置搜索参数
 */
@Data
public class PendantConfigSearchH5DTO {

    @ApiModelProperty(value = "课件的relId",required = true,dataType = "Integer")
    @NotNull(message = "课件的relId不能为空")
    private Integer relId;

}
