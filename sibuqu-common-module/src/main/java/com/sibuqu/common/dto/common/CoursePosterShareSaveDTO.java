package com.sibuqu.common.dto.common;

import com.sibuqu.common.valid.AddValid;
import com.sibuqu.common.valid.RemoveValid;
import com.sibuqu.common.valid.ShowOrHideValid;
import com.sibuqu.common.valid.UpdateValid;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 课程或班级分享图 新增或修改
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel( description = "课程或班级分享图新增或修改")
public class CoursePosterShareSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value ="主键", required = false, dataType = "Integer")
    @NotNull(message = "主键不能为空",groups = {UpdateValid.class, ShowOrHideValid.class, RemoveValid.class,})
    private Integer id;

    @ApiModelProperty( value ="课程ID", required = true, dataType = "Integer")
    @NotNull(message = "课程ID不能为空",groups = {AddValid.class})
    private Integer courseId;

    @ApiModelProperty(value ="海报名称", required = true, dataType = "String")
    @NotNull(message = "海报名称不能为空",groups = {AddValid.class,UpdateValid.class})
    private String name;

    @ApiModelProperty(value ="海报分享图地址", required = true, dataType = "String")
    @NotNull(message = "海报分享图不能为空",groups = {AddValid.class,UpdateValid.class})
    private String sharePosterUrl;

    @ApiModelProperty(value ="海报类型（1.课程海报  2.邀请加班海报 3.战略APP课程海报 4.日收获海报）", required = true, dataType = "Integer")
    @NotNull(message = "海报类型不能为空",groups = {AddValid.class,UpdateValid.class})
    private Integer type;

/*    @ApiModelProperty(value ="海报样式图地址集合多个逗号隔开", required = true, dataType = "List")
    @NotEmpty(message = "海报样式不能为空",groups = {AddValid.class,UpdateValid.class})
    private List<String> posterStyleUrlList;*/
    @ApiModelProperty(value ="海报样式图地址", required = true, dataType = "String")
    private String posterStyleUrl;

    @ApiModelProperty(value ="海报样式的第几张 1开始", required = true, dataType = "Integer")
    private Integer posterStyleIndex;

    @ApiModelProperty(value ="分享导语")
    @NotNull(message = "分享导语不能为空",groups = {AddValid.class,UpdateValid.class})
    private String shareWords;

    @ApiModelProperty(value ="扫码的导语")
    //@NotNull(message = "分享导语不能为空",groups = {AddValid.class,UpdateValid.class})
    private String scanWords;

    @ApiModelProperty(value ="排序", required = true, dataType = "Integer")
    @NotNull(message = "排序不能为空",groups = {AddValid.class,UpdateValid.class})
    private Integer sort;

    @ApiModelProperty(value ="状态1显示2隐藏", required = true, dataType = "Integer")
    @NotNull(message = "状态不能为空",groups = {AddValid.class,UpdateValid.class,ShowOrHideValid.class})
    private Integer dataFlag;

}
