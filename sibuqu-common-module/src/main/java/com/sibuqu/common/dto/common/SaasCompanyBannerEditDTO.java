package com.sibuqu.common.dto.common;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/12/22 17:17
 * @Description Saas平台下企业的banner编辑
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "Saas平台下企业的banner编辑参数", description = "Saas平台下企业的banner编辑参数")
public class SaasCompanyBannerEditDTO implements Serializable {

    //@ApiModelProperty(value = "主键信息id",dataType = "Integer")
    //@NotNull(message = "主键信息id不能为空")
    //private Integer id;


    @ApiModelProperty("标题")
    @TableField("title")
    private String title;

    @ApiModelProperty("跳转连接")
    private String url;

    @ApiModelProperty("图片地址")
    private String imgUrl;

    @ApiModelProperty("是否需要跳转 0-需要跳转；1-不需要")
    private Integer jumpNeed;

    @ApiModelProperty("跳转类型：0-自定义连接；1-课程；2-课件；3-直播；4-图文内容")
    private Integer jumpType;

    @ApiModelProperty("展示顺序")
    private Integer sortNum;

    @ApiModelProperty("展示状态：1-展示；2-禁用")
    private Integer dataFlag;

}
