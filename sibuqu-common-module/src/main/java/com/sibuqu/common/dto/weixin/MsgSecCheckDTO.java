package com.sibuqu.common.dto.weixin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class MsgSecCheckDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    // content	string	是	需检测的文本内容，文本字数的上限为2500字，需使用UTF-8编码
    // version	number	是	接口版本号，2.0版本为固定值2
    // scene	number	是	场景枚举值（1 资料；2 评论；3 论坛；4 社交日志）
    // openid	string	是	用户的openid（用户需在近两小时访问过小程序）
    // title	string	否	文本标题，需使用UTF-8编码
    // nickname	string	否	用户昵称，需使用UTF-8编码
    // signature	string	否	个性签名，该参数仅在资料类场景有效(scene=1)，需使用UTF-8编码

    @ApiModelProperty(value = "需检测的文本内容，文本字数的上限为2500字，需使用UTF-8编码")
    @NotNull(message = "需检测的文本内容不能为空")
    private String content;

    @ApiModelProperty(value = "接口版本号，2.0版本为固定值2")
    private Integer version = 2;

    @ApiModelProperty(value = "场景枚举值（1 资料；2 评论；3 论坛；4 社交日志）")
    @NotNull(message = "场景枚举值不能为空")
    private Integer scene;

    @ApiModelProperty(value = "非必填 用户的openid（用户需在近两小时访问过小程序）")
    private String miniOpenid;

    @ApiModelProperty(value = "用户 id")
    @NotNull(message = "用户 id 不能为空")
    private Integer userId;

    @ApiModelProperty(value = "文本标题，需使用UTF-8编码")
    private String title;

    @ApiModelProperty(value = "用户昵称，需使用UTF-8编码")
    private String nickname;

    @ApiModelProperty(value = "个性签名，该参数仅在资料类场景有效(scene=1)，需使用UTF-8编码")
    private String signature;

    @ApiModelProperty(value = "小程序类型 1-润泽园官方 2-天地常新")
    private String miniType = "1";


}
