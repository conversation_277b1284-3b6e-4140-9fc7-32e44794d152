package com.sibuqu.common.dto;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 分页参数
 */
@Data
public class PageBase {
    @ApiModelProperty(value = "第几页")
    private int pageNum;
    @ApiModelProperty("分页大小")
    private int pageSize;
    private List<String> descs;
    private List<String> ascs;
    private LocalDateTime standardTime;

    public <T> Page<T> getPage(){
        Page<T> page = new Page<>();
        List<OrderItem> orderItems = getOrderItem();
        if (!CollectionUtils.isEmpty(orderItems)) {
            page.setOrders(orderItems);
        }
        if (pageSize < 1) {
            page.setSize(300);
        } else {
            page.setSize(pageSize);
        }
        page.setCurrent(pageNum);
        return page;
    }


    private List<OrderItem> getOrderItem() {
        List<OrderItem> orderItems = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ascs)) {
            ascs.forEach(asc -> orderItems.add(OrderItem.asc(asc)));
        }
        if (!CollectionUtils.isEmpty(descs)) {
            descs.forEach(desc -> orderItems.add(OrderItem.desc(desc)));
        }
        return orderItems;
    }
}
