package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/9/14 10:30
 * @Version 1.0
 * @Description 直播评论管理查询参数
 **/
@Data
public class CommentLiveSearchDTO   {

    @ApiModelProperty(value = "关联课程时间表ID courseTimetableId",required = true)
    @NotNull(message = "关联课程时间表ID不能为空")
    private Integer  courseTimetableId;

    @ApiModelProperty(value = "评论的内容")
    private String content;

    @ApiModelProperty("用户姓名")
    private String  userName;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty("用户手机")
    private Integer phone;
    @ApiModelProperty(value = "状态(0:隐藏1:显示)")
    private Integer  show;
    @ApiModelProperty(value = "发布时间开始时间")
    private String  startDate;

    @ApiModelProperty(value = "发布时间结束时间")
    private String  endDate;

    @ApiModelProperty(value = "第几页")
    private int pageNum;
    @ApiModelProperty("分页大小")
    private int pageSize;

    @ApiModelProperty(value = "审核状态(0:AI自动判断未通过，1AI自动判断通过，2：人工审核未通过,3:人工审核通过)")
    private List<Integer> checkStatus;

    @ApiModelProperty(value = "楼层的类别")
    private Integer msgType;
}
