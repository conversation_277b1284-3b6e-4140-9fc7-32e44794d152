package com.sibuqu.common.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 微信下的消息模板消息 保存
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Data
@Accessors(chain = true)
@ApiModel( description = "解析服务号的消息内容")
public class MsgTempAnalysisJsonDTO implements Serializable {

    @ApiModelProperty(value = "服务号模板的内容",required = true)
    @NotNull(message ="服务号模板的内容不能为空" )
    private String templateContent;

}
