package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/22 14:20
 * @Description 角色修改参数
 */
@Data
public class SysRoleUpdateDTO implements Serializable {

    @ApiModelProperty(value = " 主键", required = false, dataType = "Integer")
    private Integer roleId;

    @ApiModelProperty(value = "角色名称", required = false, dataType = "String")
    private String roleName;

    @ApiModelProperty(value = "角色code", required = false, dataType = "String")
    private String roleCode;

    @ApiModelProperty(value = "角色描述", required = false, dataType = "aaaaa")
    private String roleDesc;

    @ApiModelProperty(value = "排序", required = false, dataType = "aaaaa")
    private Integer sort;

    @ApiModelProperty(value = "是否启用（0禁用，1启用）", required = false, dataType = "Byte")
    private Byte status;

    @ApiModelProperty(value = "菜单的主键信息", required = true, dataType = "List")
    private List<Integer> menuIds;

}