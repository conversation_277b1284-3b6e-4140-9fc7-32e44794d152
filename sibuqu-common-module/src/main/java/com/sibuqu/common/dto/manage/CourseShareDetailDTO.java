package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/22 14:20
 * @Description 课程分享配置详情
 */
@Data
public class CourseShareDetailDTO implements Serializable {

    @ApiModelProperty(value = "课程Id", required = true, dataType = "Integer")
    @NotNull(message = "课程Id不能为空")
    private Integer courseId;

    @ApiModelProperty(value = "企业ID（个人版传 -1）", required = true, dataType = "Integer")
    @NotNull(message = "企业ID不能为空")
    private Integer companyId;

}