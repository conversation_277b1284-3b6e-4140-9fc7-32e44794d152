package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class CourseOnlineUserSendMqttMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("操作类型 onlineUser")
    private String oper = "onlineUser";

    @ApiModelProperty("课件ID")
    private Integer courseTimetableId;

    @ApiModelProperty("资源类型(1音频 2视频 3直播)")
    private Integer resourceType;

    @ApiModelProperty("总人数")
    private Long userTotal = 1L;

    @ApiModelProperty("在学用户预览")
    private List<Integer> userIds = new ArrayList<>();

    @ApiModelProperty("数据类型 1-正在学习 2-历史学习")
    private Integer dateType = 1;

}
