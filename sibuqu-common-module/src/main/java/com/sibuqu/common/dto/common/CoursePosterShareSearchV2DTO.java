package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
@ApiModel(description = "课程或班级分享图搜索列表")
public class CoursePosterShareSearchV2DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程ID ( 1.课程海报 必传)", required = false, dataType = "Integer")
    private Integer courseId;

    @ApiModelProperty(value = "海报类型（1.课程海报  2.邀请加班海报 3.战略APP课程海报 4.日收获海报）", required = true, dataType = "Integer")
    @NotNull(message = "海报类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "班级 id")
    private Integer classesId;
}
