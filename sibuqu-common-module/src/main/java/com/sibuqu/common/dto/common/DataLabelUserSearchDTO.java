package com.sibuqu.common.dto.common;

import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description SaaS后台数据标签人员查询DTO
 * @createTime 2022年5月31日14:22:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DataLabelUserSearchDTO extends PageBase implements Serializable {

    @ApiModelProperty("企业ID")
    private Integer companyId;

    @ApiModelProperty("数据标签ID")
    @NotNull(message = "数据标签ID不能为空")
    private Integer dataLabelId;

}
