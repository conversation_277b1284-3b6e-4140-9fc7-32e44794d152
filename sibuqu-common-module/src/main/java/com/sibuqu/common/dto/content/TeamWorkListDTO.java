package com.sibuqu.common.dto.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TeamWorkListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "每页数量")
    private Integer pageSize;

    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @ApiModelProperty("团队 id")
    private Long teamId;

    @ApiModelProperty(value = "type 1-最热 2-最新")
    private Integer type;

}