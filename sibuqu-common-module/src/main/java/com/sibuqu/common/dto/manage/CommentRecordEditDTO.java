package com.sibuqu.common.dto.manage;

import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/07/26 11:48
 * @Description 后台管理系统 评论编辑
 */
@Data
public class CommentRecordEditDTO implements Serializable {

    @ApiModelProperty(value = "企业ID", required = true, dataType = "Integer")
    @NotNull(message = "企业ID不能为空")
    private Integer companyId;
    /*
        @ApiModelProperty(value = "课程id",required = true)
        @NotNull(message = "课程id不能为空")
        private Integer courseId;*/
    @ApiModelProperty(value = "主键id",required = true)
    @NotNull(message = "主键不能为空")
    private Integer id;

    @ApiModelProperty(value = "显示状态（0.隐藏 1.显示）",required = true)
    private Integer dataFlag;

}
