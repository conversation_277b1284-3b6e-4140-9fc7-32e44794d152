package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 修改企业用户名称dto
 */
@Data
public class CompanyUserNameModifyDTO {
    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Integer userId;

    @ApiModelProperty("企业id")
    private Integer companyId;
    /**
     * 老的真实名称
     */
    @ApiModelProperty("老的真实名称")
    private String oldRealName;
    /**
     * 新的真实名称
     */
    @ApiModelProperty("新的真实名称")
    private String newRealName;
}
