package com.sibuqu.common.dto.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class GetContentListByDataIdSubDataIdDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关联数据类型 1-课程下班级内容 2-广场 3-专区 4-inno周作业")
    @NotNull(message = "关联数据类型不能为空")
    private Integer dataType;

    @ApiModelProperty(value = "关联数据id type=1为课程id")
    @NotNull(message = "关联数据id不能为空")
    private Long dataId;

    @ApiModelProperty(value = "关联子数据id type=1为班级id")
    private Long subDataId = 0L;

    @ApiModelProperty(value = "用户 id")
    private Integer userId;

}