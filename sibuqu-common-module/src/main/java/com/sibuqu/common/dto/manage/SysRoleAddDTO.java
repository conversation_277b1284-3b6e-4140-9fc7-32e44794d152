package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/22 14:19
 * @Description 角色添加参数
 */
@Data
public class SysRoleAddDTO implements Serializable {

    @ApiModelProperty(value = "角色名称",required = true,dataType = "String")
    private String roleName;

    @ApiModelProperty(value = "角色描述",required = false,dataType = "String")
    private String roleDesc;

    @ApiModelProperty(value = "是否启用（0禁用，1启用）",required = true,dataType = "String")
    private String status;

    @ApiModelProperty(value = "菜单的主键信息",required = true,dataType = "List")
    private List<Integer> menuIds;


}