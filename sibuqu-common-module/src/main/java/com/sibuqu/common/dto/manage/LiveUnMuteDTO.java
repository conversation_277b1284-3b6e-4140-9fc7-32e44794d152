package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/6/20-7:13 下午
 *   解除禁言
 */
@Data
public class LiveUnMuteDTO {
    /**
     * 禁言的主键id
     */
    @ApiModelProperty(value = "禁言的主键id",required = true,dataType = "Integer")
    @NotNull
    private Integer id;
//    /**
//     * 用户的userId
//     */
//    @ApiModelProperty(value = "用户的userId,uid",required = true,dataType = "Integer")
//    private Integer userId;
//    /**
//     * 直播的房间号id
//     */
//    @ApiModelProperty(value = "直播间的roomId",required = true,dataType = "Integer")
//    @NotNull
//    private Integer roomId;
//    /**
//     * 解除禁言类型 0 本次解除禁言,1全部解除禁言
//     */
//    @ApiModelProperty(value = "解除禁言类型 0 本次解除禁言,1全部解除禁言",required = true,dataType = "Integer")
//    @NotNull
//    private Integer speakType;
}
