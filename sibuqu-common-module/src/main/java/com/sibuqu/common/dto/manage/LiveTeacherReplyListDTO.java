package com.sibuqu.common.dto.manage;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/10-11:23
 * 直播老师回复 图片 文本 列表信息
 */

@Data
public class LiveTeacherReplyListDTO  extends PageBase {

   // @ApiModelProperty(value = "资源id")
  //  private Integer resourceId;

    @ApiModelProperty(value = "关联课程时间表ID",required = true,dataType = "Integer")
    private Integer courseTimetableId;
    /**
     * 资源id
     */
    //@ApiModelProperty(value = "资源id",required = true,dataType = "Integer")
   // private Integer resourceId;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id",required = false,dataType = "Integer")
    private Integer userId;
    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称",required = false,dataType = "Integer")
    private String createUserName;
    /**
     * 消息类型，0：文本 1.图片 3.图片和文字
     */
    @ApiModelProperty(value = "消息类型，0：文本 1.图片 3.图片和文字 4.公告消息",required = false,dataType = "Integer")
    private Integer replyType;

    /**
     * 用户楼层字典id
     */
//    @ApiModelProperty(value = "用户楼层字典id",required = false,dataType = "Integer")
//    private Integer msgType;
    /**
     * 课程类型200易经
     */
//    @ApiModelProperty(value = "课程类型",required = false,dataType = "Integer")
//    private Integer courseType;
    /**
     * 是否已发送 0：否 1：是
     */
    @ApiModelProperty(value = "是否已发送 0:否 1:是",required = false,dataType = "Integer")
    private Integer isSend;

    /**
     * 更新的开始时间
     */
    @ApiModelProperty(value ="更新开始时间",required = false,dataType = "String")
    private String startTime;
    /**
     * 更新的结束时间
     */
    @ApiModelProperty(value ="更新结束时间",required = false,dataType = "String")
    private String endTime;

}
