package com.sibuqu.common.dto.common;

import com.sibuqu.common.valid.AddValid;
import com.sibuqu.common.valid.RemoveValid;
import com.sibuqu.common.valid.ShowOrHideValid;
import com.sibuqu.common.valid.UpdateValid;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 保存战略APP课程分享海报配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025年7月2日16:17:31
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel( description = "保存战略APP课程分享海报配置")
public class InnoCoursePosterShareSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty( value ="课程ID", required = true, dataType = "Integer")
    @NotNull(message = "课程ID不能为空",groups = {AddValid.class})
    private Integer courseId;

    @ApiModelProperty(value ="海报分享图地址", required = true, dataType = "String")
    @NotNull(message = "海报分享图不能为空",groups = {AddValid.class,UpdateValid.class})
    private String sharePosterUrl;

}
