package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2021/10/22 12:07
 * @Description 用户手机验证
 */
@Data
public class SysUserPhoneDTO {

    @ApiModelProperty(value ="用户id,新增账号的时候传入空", required = false, dataType = "String")
    private String userId;

    @ApiModelProperty(value ="用户手机号", required = true, dataType = "String")
    @NotNull(message = "手机号不能为空")
    private String phone;
}
