package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/22 11:29
 * @Description 修改账号信息参数
 */
@Data
public class SysUserUpdateDTO {

    @ApiModelProperty(value = "主键", required = true, dataType = "Integer")
    private Integer id;

    @ApiModelProperty(value = "用户姓名", required = true, dataType = "String")
    @NotNull(message = "姓名不能为空")
    private String personName;

    @ApiModelProperty(value = "手机号", required = true, dataType = "String")
    @NotNull(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "密码", required = true, dataType = "String")
    private String password;

    @ApiModelProperty(value = "账号状态 0:禁用 1：启用", required = true, dataType = "String")
    private String state;

    @ApiModelProperty(value = "角色的id集合", required = false, dataType = "List<Integer>")
    private List<Integer> roleIds;



}
