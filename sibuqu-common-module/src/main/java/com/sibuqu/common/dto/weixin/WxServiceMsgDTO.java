package com.sibuqu.common.dto.weixin;

/**
 * @ClassName WxServiceMsgDTO.java
 * <AUTHOR>
 * @Version 1.0.0
 * @Description TODO
 * @CreateTime 2024年04月26日 09:31:31
 */
import lombok.Data;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.io.StringReader;

/**
 * 公众号返回参数封装
 */
@XmlRootElement(name = "xml")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class WxServiceMsgDTO implements Serializable {

    @XmlElement(name = "Event")
    private String event;

    @XmlElement(name = "Content")
    private String content;

    @XmlElement(name = "MsgType")
    private String msgType;

    @XmlElement(name = "ToUserName")
    private String toUserName;

    @XmlElement(name = "EventKey")
    private String eventKey;

    @XmlElement(name="CreateTime")
    private String createTime;

    /**
     * fromUserName为关注人的openId
     **/
    @XmlElement(name = "FromUserName")
    private String fromUserName;


    @XmlElement(name = "Ticket")
    private String ticket;

    @XmlElement(name = "Encrypt")
    private String encrypt;

    @XmlElement(name = "MsgId")
    private String msgId;


    public static WxServiceMsgDTO xmlToEntity(String xml) {
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(WxServiceMsgDTO.class);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

            StringReader reader = new StringReader(xml);
            WxServiceMsgDTO xmlEntity = (WxServiceMsgDTO) unmarshaller.unmarshal(reader);

            System.out.println("ToUserName: " + xmlEntity.getToUserName());
            System.out.println("FromUserName: " + xmlEntity.getFromUserName());
            System.out.println("CreateTime: " + xmlEntity.getCreateTime());
            System.out.println("MsgType: " + xmlEntity.getMsgType());
            System.out.println("Event: " + xmlEntity.getEvent());
            System.out.println("EventKey: " + xmlEntity.getEventKey());
            System.out.println("Ticket: " + xmlEntity.getTicket());
            System.out.println("Encrypt: " + xmlEntity.getEncrypt());
            System.out.println("Content: " + xmlEntity.getContent());
            System.out.println("MsgType: " + xmlEntity.getMsgType());
            System.out.println("MsgId: " + xmlEntity.getMsgId());
            return xmlEntity;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return  null;
    }




    public static void main(String[] args) {
        String xml = "<xml><ToUserName><![CDATA[gh_4aeb291d8a22]]></ToUserName><FromUserName><![CDATA[o65x_6GTjlJP4JERgiD9AYXS1h54]]></FromUserName><CreateTime>1714098141</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[SCAN]]></Event><EventKey><![CDATA[0]]></EventKey><Ticket><![CDATA[gQHu8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyemI0Rko2SHNkX0UxMFVYUXhDMUsAAgS4ACtmAwSAOgkA]]></Ticket><Encrypt><![CDATA[cQO0vJ3Tqmvw12N5O6OXxARyoGrKrzJd2zZggTPkL7ppt1SjZ24pfCZDQJ+h30LnYicL+Ddx4Tjzbj/GacaOVGn2vogFE0mW7jnEpOqdLfywuo/mc2L2TxfJjdlTW/rDoKymIlMy369iIEr9DdOhowZDiZVCYQxkA7JsDHPyGp+lwBOFnXgMkC4IbKTY1D7huzI+OHHgtCaJnmSWhYmQhUO6ftVFIff2GquyuBrCqARRAGhgIcc202myp+GpyFlhZGfq1SBnO0ieg515PkhhUciYeg7LHgwITLDyvusRAgAvgQAy6Lhac0ROogZ4Je7GD3fn2CPQ70bc4XIerUy5/abFwMQ+YaFcirC1kuaB8YSPUgACuEntgIvzz2ibBbPvb4KzXUNh9WFHdzkktlv4Rl/To5G2LNKFwjSdVC4/TYYQa1yR+97GkzI+1cB6oRx21cuYRn+uhyRzWyUd0s3sHGDk+HC3TIW4NY4AaGDjgogDQaifK86H9r2+vUjx+ksv7513veCfAe+irIRGUevTIqLnVamOtXVzFFoWh/EU4lRw6ksES/nrCnqCAzcuxODxWPfNSo0CAkTkAfJcn5gBPA==]]></Encrypt></xml>";

        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(WxServiceMsgDTO.class);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

            StringReader reader = new StringReader(xml);
            WxServiceMsgDTO xmlEntity = (WxServiceMsgDTO) unmarshaller.unmarshal(reader);

            System.out.println("ToUserName: " + xmlEntity.getToUserName());
            System.out.println("FromUserName: " + xmlEntity.getFromUserName());
            System.out.println("CreateTime: " + xmlEntity.getCreateTime());
            System.out.println("MsgType: " + xmlEntity.getMsgType());
            System.out.println("Event: " + xmlEntity.getEvent());
            System.out.println("EventKey: " + xmlEntity.getEventKey());
            System.out.println("Ticket: " + xmlEntity.getTicket());
            System.out.println("Encrypt: " + xmlEntity.getEncrypt());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
