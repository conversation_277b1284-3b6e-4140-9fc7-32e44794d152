package com.sibuqu.common.dto.weixin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class MediaCheckAsyncDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    //media_url	string	是	要检测的图片或音频的url，支持图片格式包括jpg, jepg, png, bmp, gif（取首帧），支持的音频格式包括mp3, aac, ac3, wma, flac, vorbis, opus, wav
    //media_type	number	是	1:音频;2:图片
    //version	number	是	接口版本号，2.0版本为固定值2
    //scene	number	是	场景枚举值（1 资料；2 评论；3 论坛；4 社交日志）
    //openid	string	是	用户的openid（用户需在近两小时访问过小程序）

    @ApiModelProperty("要检测的图片或音频的url，支持图片格式包括jpg, jepg, png, bmp, gif（取首帧），支持的音频格式包括mp3, aac, ac3, wma, flac, vorbis, opus, wav")
    @NotNull(message = "media_url不能为空")
    private String mediaUrl;

    @ApiModelProperty("1:音频;2:图片")
    @NotNull(message = "media_type不能为空")
    private Integer mediaType;

    @ApiModelProperty("接口版本号，2.0版本为固定值2")
    private Integer version = 2;

    @ApiModelProperty("场景枚举值（1 资料；2 评论；3 论坛；4 社交日志）")
    @NotNull(message = "scene不能为空")
    private Integer scene;

    @ApiModelProperty(value = "用户 id")
    @NotNull(message = "用户 id 不能为空")
    private Integer userId;

    @ApiModelProperty(value = "非必填 用户的openid（用户需在近两小时访问过小程序）")
    private String miniOpenid;

    @ApiModelProperty(value = "小程序类型 1-润泽园官方 2-天地常新")
    private String miniType = "1";

}
