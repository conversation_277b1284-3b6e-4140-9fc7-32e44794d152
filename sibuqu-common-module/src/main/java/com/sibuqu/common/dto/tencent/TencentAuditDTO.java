package com.sibuqu.common.dto.tencent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TencentAuditDTO {
    @ApiModelProperty("数据主键id")
    private Integer id;
    @ApiModelProperty("媒体内容（图片，音频，视频使用逗号分割）")
    private String content;
    @ApiModelProperty("媒体文件类型 1文本 2图片 3音频 4视频")
    private Integer mediaType;
    @ApiModelProperty("业务类型 异步消息kafka需要使用，根据业务类型获取消息")
    private String businessType;

}
