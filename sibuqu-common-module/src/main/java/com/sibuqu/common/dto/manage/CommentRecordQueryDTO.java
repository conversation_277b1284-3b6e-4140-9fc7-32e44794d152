package com.sibuqu.common.dto.manage;

import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/07/26 11:48
 * @Description 后台管理系统 评论查询
 */
@Data
public class CommentRecordQueryDTO extends PageBase implements Serializable {

    @ApiModelProperty(value = "企业ID", required = true, dataType = "Integer")
    @NotNull(message = "企业ID不能为空")
    private Integer companyId;

    @ApiModelProperty(value = "课程id",required = true)
    @NotNull(message = "课程id不能为空")
    private Integer courseId;

    //@ApiModelProperty(value = "课程资源ID",required = false)
   // private Integer resourceId;

    @ApiModelProperty(value = "课程表ID",required = false)
    private Integer courseTimetableId;

   /* @ApiModelProperty(value = "平台类型（0.企业版 1.个人版）",notes = "平台类型（0.企业版 1.个人版）")
    private Integer platformType = 0;*/

   // @ApiModelProperty("资源标题名称(课件名称)")
   // private String resourceTitle;

    @ApiModelProperty(value = "用户姓名",  dataType = "String")
    private String username;


    @ApiModelProperty("用户手机号")
    private String userPhone;


    @ApiModelProperty(value = "评论开始时间 格式：yyyy-MM-dd", dataType = "String")
    private String startTime;
    @ApiModelProperty(value = "评论结束时间 格式：yyyy-MM-dd", dataType = "String")
    private String endTime;


    @ApiModelProperty("显示状态（0.隐藏 1.显示）")
    private Integer dataFlag;

}
