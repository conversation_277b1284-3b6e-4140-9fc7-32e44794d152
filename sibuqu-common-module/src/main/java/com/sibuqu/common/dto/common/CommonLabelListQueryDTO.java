package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CommonLabelListQueryDTO {

    @ApiModelProperty("商品ID")
    @NotNull(message = "商品ID不能为空")
    private Integer goodsId;

    @ApiModelProperty("标签类型 1-心得标签 2-商品标签 3-内容标签")
    @NotNull(message = "标签类型不能为空")
    private List<Integer> labelTypes;
}
