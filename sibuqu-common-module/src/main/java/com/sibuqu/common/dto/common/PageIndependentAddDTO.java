package com.sibuqu.common.dto.common;

import com.sibuqu.common.vo.common.AnnexVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

@Data
public class PageIndependentAddDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "页面组 id")
    private Integer pageGroupId;

    @ApiModelProperty(value = "标题")
    @Length(max = 16, message = "标题最长 16 位")
    private String title;

    @ApiModelProperty(value = "页面类型 1:富文本&图片 2:带视频海报")
    private Integer independentPageType;

    @ApiModelProperty(value = "背景图片（多个用逗号分割）")
    private String bgUrl;

    @ApiModelProperty(value = "页眉图片")
    private String headerUrl;

    @ApiModelProperty(value = "页面内容")
    private String content;

    @ApiModelProperty(value = "页脚图片")
    private String footerUrl;

    @ApiModelProperty(value = "视频封面图")
    private String videoCoverUrl;

    @ApiModelProperty(value = "视频地址")
    private String videoUrl;

    @ApiModelProperty(value = "是否显示按钮 0 不显示 1 显示")
    private Boolean buttonFlag;

    @ApiModelProperty(value = "按钮底色")
    private String buttonBgcolor;

    @ApiModelProperty(value = "按钮颜色")
    private String buttonColor;

    @ApiModelProperty(value = "按钮文字")
    @Length(max = 16, message = "按钮文字最长 16 位")
    private String buttonContent;

    @ApiModelProperty(value = "按钮跳转类型 1:URL 2:课程详情页 3:课程购买支付页 4:兑换码购买支付页 5:独立直播 6:下载图片")
    private Integer buttonJumpType;

    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @ApiModelProperty(value = "课件id")
    private Integer relId;

    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    @ApiModelProperty(value = "按钮跳转url")
    private String buttonJumpUrl;

    @ApiModelProperty(value = "直播标题")
    private String liveTitle;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "微信分享展示标题")
    private String weixinShareTitle;

    @ApiModelProperty(value = "微信分享描述")
    private String weixinShareDesc;

    @ApiModelProperty(value = "微信分享图片 url")
    private String weixinSharePicUrl;

    @ApiModelProperty("附件列表")
    private List<AnnexVO> annexList;

}
