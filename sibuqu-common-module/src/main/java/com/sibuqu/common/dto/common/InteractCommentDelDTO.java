package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/25 9:48
 * @Description 互动评论删除入参
 */
@Data
public class InteractCommentDelDTO implements Serializable {

    @ApiModelProperty(value = "平台类型（0.企业版 1.个人版）",notes = "平台类型（0.企业版 1.个人版）",required = true)
    private Integer platformType = 0;

    @ApiModelProperty(value = "数据ID,心得评论对应作业id,课件资源点赞对应课件id")
    @NotNull(message = "数据ID不能为空")
    private Integer dataId;

    @ApiModelProperty(value = "评论ID",notes = "评论ID",required = true)
    @NotNull(message = "评论ID不能为空")
    private Integer id;

    @ApiModelProperty(value = "删除类型（1.删除评论 2.删除回复）")
    @NotNull(message = "删除类型不能为空")
    private Integer sendType;

    @ApiModelProperty(value = "评论类型 1-心得评论 2-课件评论 3-成长专区评论")
    @NotNull(message = "评论数据类型不能为空")
    private Integer commentType;

}
