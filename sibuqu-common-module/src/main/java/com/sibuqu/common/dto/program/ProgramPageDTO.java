package com.sibuqu.common.dto.program;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ProgramPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目名称")
    private String programName;

    @ApiModelProperty("当前页数")
    @NotNull(message = "当前页数不能为空")
    @Min(value = 1, message = "当前页数不能小于1")
    private int pageNum;

    @ApiModelProperty("分页大小")
    @NotNull(message = "分页大小不能为空")
    @Max(value = 500, message = "分页大小不能超过500")
    private int pageSize;


}