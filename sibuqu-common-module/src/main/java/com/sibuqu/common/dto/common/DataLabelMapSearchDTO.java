package com.sibuqu.common.dto.common;

import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 内部服务查询数据标签MapDTO
 * @createTime 2022年5月31日14:22:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataLabelMapSearchDTO implements Serializable {

    @ApiModelProperty("企业ID")
    @NotNull(message = "企业ID不能为空")
    private Integer companyId;

    @ApiModelProperty("数据标签ID")
    @NotNull(message = "数据标签ID不能为空")
    private List<Integer> dataLabelIds;

}
