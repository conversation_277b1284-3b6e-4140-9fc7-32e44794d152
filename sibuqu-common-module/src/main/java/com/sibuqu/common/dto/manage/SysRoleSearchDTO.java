package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/22 14:22
 * @Description 角色查询参数
 */
@Data
public class SysRoleSearchDTO implements Serializable {

    @ApiModelProperty(value = "角色名称", required = true, dataType = "String")
    private String roleName;

    @ApiModelProperty(value = "是否启用（0禁用，1启用）", required = true, dataType = "String")
    private String status;

    @ApiModelProperty(value = "第几页", required = false, dataType = "int")
    private int pageNum;

    @ApiModelProperty(value = "分页大小", required = false, dataType = "int")
    private int pageSize;


}