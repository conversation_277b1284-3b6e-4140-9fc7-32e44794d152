package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

@Data
public class VersionConfigQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备类型 ANDROID IOS PC_M PC_W PC_HOT IM")
    private String deviceType;

    @ApiModelProperty("app来源 1-润泽园app 2-战略创新app")
    private Integer appSource;


}