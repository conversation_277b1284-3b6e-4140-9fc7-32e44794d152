package com.sibuqu.common.dto.common;

import com.sibuqu.common.vo.common.LivePendantInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LivePendantUpDownMqttDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("课件 id")
    private Integer courseTimetableId;

    @ApiModelProperty("挂件列表")
    private List<LivePendantInfoVO> list;

}
