package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LikeStatusByDataIdAndUserIdListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户ID列表")
    private List<Integer> userIdList;

    @ApiModelProperty(value = "点赞类型 1-心得点赞 2-课件评论点赞 3-课件资源点赞 4-成长专区点赞 5-小程序心得点赞 6-小程序视频点赞 7-小程序会议点赞 11-小组文字点赞 12-小组视频点赞 13-小程序会议问题 14-家庭幸福暑期活动 15-小组共读 16-家书 17-成长案例 18-知行卡践行 19-小程序打卡 20-共读任务 21-共读任务记录 22-想法 23-十家连心周报 24-润泽头条音频点赞 25-橐龠会议内容 26-企业主页 27-用户学习排行榜 28-成长计划心得 29-课程笔记 30-公共内容 31-信箱 32-公共小视频 33-评论 34-回复")
    private Integer likeType;

    @ApiModelProperty("数据ID")
    private Integer dataId;

}
