package com.sibuqu.common.dto.program;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ProgramAddDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目名称")
    private String programName;

    @ApiModelProperty(value = "项目描述")
    private String programDesc;

}