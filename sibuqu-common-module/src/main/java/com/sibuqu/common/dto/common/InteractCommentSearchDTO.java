package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/25 9:48
 * @Description 互动评论搜索入参
 */
@Data
public class InteractCommentSearchDTO implements Serializable {

    @ApiModelProperty(value = "平台类型（0.企业版 1.个人版）",notes = "平台类型（0.企业版 1.个人版）",required = true)
    private Integer platformType = 0;

    @ApiModelProperty(value = "班级ID（已加班心得评论传具体班级，未加班心得评论传0，课件评论传-1）",notes = "班级ID（已加班心得评论传具体班级，未加班心得评论传0，课件评论传-1）",required = true)
    private Integer classesId;

    @ApiModelProperty(value = "数据ID,心得评论对应作业id,课件资源点赞对应课件id")
    @NotNull(message = "数据ID不能为空")
    private Integer dataId;

    @ApiModelProperty(value = "评论类型 1-心得评论 2-课件评论 3-成长专区评论 4-小程序心得评论 5-小程序视频评论 6-小程序会议评论 11-小组文字评论 12-小组视频评论 13-小程序会议问题 14-家庭幸福暑期活动 15-小组共读 16-家书 17-成长案例 19-小程序打卡 20-共读任务 21-共读任务记录 22-想法 23-十家连心周报 25-橐龠会议内容 28-成长计划心得 29-课程笔记 30-公共内容 31-信箱")
    @NotNull(message = "评论类型不能为空")
    private Integer commentType;

    @ApiModelProperty(value = "是否只展示置顶评论（1是 0否）",notes = "是否只展示置顶评论")
    private Integer isOnlyShowTop;

    @ApiModelProperty(value = "页码")
    private int pageNum = 1;

    @ApiModelProperty("分页大小")
    private int pageSize = 10;

    private Integer userId;

    private String userName;

    @ApiModelProperty(value = "评论id,把当前被点赞的评论放到列表前面")
    private Integer commentId;

    @ApiModelProperty(value="企业 id")
    private Integer companyId;

    @ApiModelProperty(value="分享人的企业 id")
    private Integer shareCompanyId;

    @ApiModelProperty(value = "是否只看本企业的 1-只看本企业的 0-全部")
    private Integer onlyMyCompany;

    @ApiModelProperty("课程id")
    private Integer courseId;

}
