package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class QueryDistanceByLonLatDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("起点经度")
    @NotBlank(message = "起点经度不能为空")
    private String fromLon;

    @ApiModelProperty("起点维度")
    @NotBlank(message = "起点维度不能为空")
    private String fromLat;

    @ApiModelProperty("终点经度")
    @NotBlank(message =  "终点经度不能为空")
    private String toLon;

    @ApiModelProperty("终点维度")
    @NotBlank(message =  "终点维度不能为空")
    private String toLat;
}
