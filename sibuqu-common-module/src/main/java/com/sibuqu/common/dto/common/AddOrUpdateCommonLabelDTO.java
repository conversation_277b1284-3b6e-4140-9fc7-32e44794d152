package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AddOrUpdateCommonLabelDTO {
    @ApiModelProperty("标签id")
    private Long id;

    @ApiModelProperty("上级标签ID")
    private Integer parentId;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("标签类型 1-心得标签 2-商品标签 3-内容标签")
    private Integer labelType;

    @ApiModelProperty("标签级别 1-一级标签 2-二级标签")
    private Integer level;

    @ApiModelProperty("标签色值")
    private String labelColor;

    @ApiModelProperty("跳转类型 1-不跳转 2-跳转链接 3-跳转商品")
    private Integer jumpType;

    @ApiModelProperty("跳转位置")
    private String jumpLocation;

    @ApiModelProperty("描述")
    private String desc;

    @ApiModelProperty("上下架状态 0-下架 1-上架")
    private Integer dataFlag;

    @ApiModelProperty("展示商品id列表")
    private List<Integer> showRangeIds;
}
