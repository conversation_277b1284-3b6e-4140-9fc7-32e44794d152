package com.sibuqu.common.dto.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SquareContentListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("请求类型 1-发现列表 2-关注列表 3-搜索列表 4-话题下推荐列表 5-话题下最新列表 6-单个用户下列表 7-视频类型滑动列表")
    private Integer apiType;

    @ApiModelProperty("cursorId")
    private Long cursorId;

    @ApiModelProperty("搜索内容")
    private String searchContent;

    @ApiModelProperty(value = "话题id")
    private Long topicId;

    @ApiModelProperty(value = "个人主页用户id")
    private Integer visitUserId;

    @ApiModelProperty("当前内容 id")
    private Long curContentId;

}