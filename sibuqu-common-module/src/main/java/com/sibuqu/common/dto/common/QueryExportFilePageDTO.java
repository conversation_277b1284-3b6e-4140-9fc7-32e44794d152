package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryExportFilePageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关联数据类型 1-学习会直播点报名数据 2-学习会直播点数据")
    private Integer dataType;

    @ApiModelProperty(value = "页码")
    private int pageNum = 1;

    @ApiModelProperty("分页大小")
    private int pageSize = 10;

}
