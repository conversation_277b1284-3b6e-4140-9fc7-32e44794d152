package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2022/8/25 23:22
 * @Version 1.0
 * @Description 重点信息--添加实体
 **/
@Data
public class LiveAnswerSaveDTO {
    @ApiModelProperty(value = "关联课程时间表ID courseTimetableId",required = true)
    @NotNull
    private Integer  courseTimetableId;
    @ApiModelProperty(value = "内容",required = true)
    @NotNull
    private String  answer;
    @ApiModelProperty(value = "状态(0:隐藏1:显示)",required = true,dataType = "Integer")
    @NotNull
    @Min(0)
    @Max(1)
    private Integer status;
}
