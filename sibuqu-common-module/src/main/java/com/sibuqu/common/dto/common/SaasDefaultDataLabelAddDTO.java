package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description SaaS后台创建数据标签DTO
 * @createTime 2022年5月31日14:41:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaasDefaultDataLabelAddDTO implements Serializable {

    @ApiModelProperty("企业ID")
    private Integer companyId;

    @ApiModelProperty("数据标签名称")
    private String labelName;

    @ApiModelProperty("创建人ID")
    private Integer createUserId;

    @ApiModelProperty("创建人姓名")
    private String createUserName;

}
