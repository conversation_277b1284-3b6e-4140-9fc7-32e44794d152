package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class SendCommentForBackDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("评论ID")
    private Integer id;

    @ApiModelProperty("课件 id")
    private Integer courseTimetableId;

    @ApiModelProperty("评论内容")
    @Length(max = 200,message = "评论最长 200 字")
    private String content;
}
