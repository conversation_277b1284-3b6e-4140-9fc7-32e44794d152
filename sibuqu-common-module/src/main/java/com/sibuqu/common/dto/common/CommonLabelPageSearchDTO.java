package com.sibuqu.common.dto.common;

import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CommonLabelPageSearchDTO extends PageBase {

    /**
     * 非常个性化的需求开发 正常业务不要用这个字段！！！
     * 需求是只有某个润泽园小程序组 查询出指定的标签 否则 不要查出这些指定的标签
     */
    @ApiModelProperty(value = "是否是党支部页面查询 1-是 0-否")
    private Integer isDangZhiBu = 0;

    @ApiModelProperty("标签类型 1-心得标签 2-商品标签 3-内容标签")
    private List<Integer> labelType;

    @ApiModelProperty("标签名称")
    private String name;
}
