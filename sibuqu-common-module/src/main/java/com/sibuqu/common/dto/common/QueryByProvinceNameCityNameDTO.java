package com.sibuqu.common.dto.common;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryByProvinceNameCityNameDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "省名字")
    private String provinceName;

    @ApiModelProperty(value = "地市名字")
    private String cityName;

    @ApiModelProperty(value = "县名字")
    private String countyName;
}
