package com.sibuqu.common.dto.manage;

import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/8/26-15:56
 * 直播消息管理 图片 文本 列表信息
 */

@Data
public class LiveMessageManageSearchDTO extends PageBase {
    @ApiModelProperty(value ="关联课程时间表ID",required = true,dataType = "Integer")
    @NotNull
    private Integer courseTimetableId;
    /**
     * 消息类型，0：文本 1.图片 3.图片和文字 4.公告消息
     */
    @ApiModelProperty(value = "消息类型，0：文本 1.图片 3.图片和文字 4.公告消息",required = false,dataType = "Integer")
    private Integer replyType;
    /**
     * 是否已发送 0：否 1：是
     */
    @ApiModelProperty(value = "是否已发送 0:否 1:是",required = false,dataType = "Integer")
    private Integer isSend;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称",required = false,dataType = "Integer")
    private String createUserName;

    /**
     * 更新的开始时间
     */
    @ApiModelProperty(value ="更新开始时间",required = false,dataType = "String")
    private String startTime;
    /**
     * 更新的结束时间
     */
    @ApiModelProperty(value ="更新结束时间",required = false,dataType = "String")
    private String endTime;

}
