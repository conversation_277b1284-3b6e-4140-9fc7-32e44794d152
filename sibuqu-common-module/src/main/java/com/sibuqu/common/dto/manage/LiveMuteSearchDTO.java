package com.sibuqu.common.dto.manage;

import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户的禁言列表(LiveMute)实体类
 *
 * <AUTHOR>
 * @since 2021-06-21 09:35:05
 */
@Data
public class LiveMuteSearchDTO extends PageBase {
        /**
     * 禁言的用户名
     */
    @ApiModelProperty(value = "禁言的用户名",required = true,dataType = "String")
    private String muteUserName;

    /**
     * 禁言类型 0 本次禁言,1全部禁言
     */
    @ApiModelProperty(value = "禁言类型 0 本次禁言,1全部禁言",required = true,dataType = "Integer")
    private Integer speakType;
    /**
     * 禁言开始时间
     */
    @ApiModelProperty(value = "禁言开始时间",required = true,dataType = "String")
    private String muteDateStart;
    /**
     * 禁言结束时间
     */
    @ApiModelProperty(value = "禁言开始时间",required = true,dataType = "LocalDateTime")
    private String muteDateEnd;

}