package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/25 9:48
 * @Description 获取点赞记录数量搜索DTO
 */
@Data
public class LikeRecordTotalSearchDTO implements Serializable {

    @ApiModelProperty(value = "数据Ids",notes = "数据Ids",required = true)
    @NotNull(message = "数据Ids不能为空")
    private List<Integer> dataIds;

    @ApiModelProperty(value = "点赞类型（1.心得点赞 2.课件评论点赞 3.课件资源点赞）",notes = "点赞类型（1.心得点赞 2.课件评论点赞 3.课件资源点赞）",required = true)
    @NotNull(message = "点赞类型不能为空")
    private Integer likeType;
}
