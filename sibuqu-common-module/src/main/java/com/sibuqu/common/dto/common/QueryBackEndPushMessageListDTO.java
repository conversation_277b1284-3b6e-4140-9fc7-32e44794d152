package com.sibuqu.common.dto.common;

import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryBackEndPushMessageListDTO extends PageBase implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "消息发送人用户名")
    private String sendUserName;

    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "发布方式0立即发布1定时发布2不发布")
    private Integer publishType;

    @ApiModelProperty(value = "发送状态0未发送1已发送")
    private Integer sendStatus;

    @ApiModelProperty(value = "发送类型 1-所有人员 2-按照课程权限 3-指定用户")
    private Integer sendType;

}
