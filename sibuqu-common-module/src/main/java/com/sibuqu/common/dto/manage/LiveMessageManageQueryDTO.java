package com.sibuqu.common.dto.manage;

import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/8/26-15:56
 * 直播消息管理
 */

@Data
public class LiveMessageManageQueryDTO{
    @ApiModelProperty(value ="关联课程时间表ID",required = true,dataType = "Integer")
    @NotNull
    private Integer courseTimetableId;
}
