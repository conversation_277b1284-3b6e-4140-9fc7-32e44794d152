package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class SaasQueryCommentListHiddenDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("评论ID")
    @NotNull
    private Integer id;

    @ApiModelProperty("显示状态（0.隐藏 1.显示）")
    @NotNull
    private Integer dataFlag;
}
