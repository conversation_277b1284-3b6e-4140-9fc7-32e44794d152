package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class UnsubscribeOneImportUserRecordDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课件id")
    private Integer relId;

    @ApiModelProperty(value = "用户id")
    private Integer userId;
}
