package com.sibuqu.common.dto.common;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ListByAppSourcePushTypeJumpPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("app来源 1-润泽园app 2-战略创新app")
    private Integer appSource;

    @ApiModelProperty(value = "推送消息的类型：1-系统消息 2-活动消息 3-评论和回复消息 4-点赞消息 5-学习提醒 7-订单消息 8-会议消息")
    private Integer pushType;

    @ApiModelProperty(value = "跳转类型 1-新版本心得列表 2-老版本心得列表 3-课件评论回复列表 4-课件评论点赞列表 11-订单详情页 12-发票详情页 13-退款详情页 21-会议提醒 22-学员申请参会消息 23-学员会议请假消息 24-学员参会申请通过消息 25-专区学员对内容的评论 26-学员对@内容的评论 27-学员被@的消息 28-专区学员对内容的点赞 29-专区对内容的引用 30-企业版班级主页 31-十家连心页 32-用户学习排行榜 33-广场内容详情页 34-邀请加入小组")
    private Integer jumpPage;

}