package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName SensitiveWordsCheckDTO.java
 * <AUTHOR>
 * @Version 1.0.0
 * @Description 关键词检查是否包含
 * @CreateTime 2022年11月29日 14:26:20
 */
@Data
public class SensitiveWordsCheckDTO implements Serializable {

    @ApiModelProperty(value = "需要过滤的词",required = true)
    @NotNull(message = "需要过滤的词不能为空")
    private String msg;

    @ApiModelProperty(value ="敏感词所属的类型  0:适用于所有 1：适用于直播 2：适用于视频/音频 3:小程序昵称校验",required = true)
    @NotNull(message = "敏感词所属的类型不能为空")
    private Integer type;

    @ApiModelProperty(value ="用户ID",required = false)
    private Integer userId;

}
