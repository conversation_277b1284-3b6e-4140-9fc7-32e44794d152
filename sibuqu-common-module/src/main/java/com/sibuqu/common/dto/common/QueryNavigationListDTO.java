package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryNavigationListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "展示位置 1-顶部菜单 2-老版金刚区 3-新版金刚区")
    private Integer showType;

    @ApiModelProperty(value = "版本类型 1-个人版 2-所有企业 3-单个企业 4-润泽园官方小程序 5-天地常新小程序")
    private Integer editionType;

}
