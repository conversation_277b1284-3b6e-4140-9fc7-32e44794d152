package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class CheckCommentIsDeleteDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "跳转类型 1-新版本心得列表 2-老版本心得列表 3-课件评论回复列表 4-课件评论点赞列表 11-订单详情页 12-发票详情页 13-退款详情页 21-会议提醒 22-学员申请参会消息 23-学员会议请假消息 24-学员参会申请通过消息 25-专区学员对内容的评论 26-学员对@内容的评论 27-学员被@的消息 28-专区学员对内容的点赞 29-专区对内容的引用 30-企业版班级主页 31-十家连心页 32-用户学习排行榜 33-广场内容详情页 34-邀请加入小组")
    @NotNull(message = "跳转类型不能为空")
    private Integer jumpPage;

    @ApiModelProperty(value = "评论id")
    private Integer commentId;

    @ApiModelProperty(value = "内容 id")
    private Integer contentId;

}
