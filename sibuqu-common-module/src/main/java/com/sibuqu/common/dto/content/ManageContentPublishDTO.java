package com.sibuqu.common.dto.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ManageContentPublishDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "话题id列表")
    private List<Long> topicIdList;

    @ApiModelProperty("内容类型 1-文本 2-图片 3-视频 4-语音")
    private Integer type;

    @ApiModelProperty("图片、视频列表")
    private List<String> mediaList;

    @ApiModelProperty("视频封面图")
    private String cover;

    @ApiModelProperty(value = "内容展示状态 0-不展示 1-展示")
    private Integer showStatus;

    @ApiModelProperty(value = "马甲用户 id")
    private Integer publishUserId;

    @ApiModelProperty(value = "内容定时发布时间")
    private LocalDateTime beginTime;
}