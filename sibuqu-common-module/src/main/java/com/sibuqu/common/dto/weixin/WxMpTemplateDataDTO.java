package com.sibuqu.common.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName WxMpTemplateDataDTO.java
 * <AUTHOR>
 * @Version 1.0.0
 * @Description 服务号模板实际发送的信息
 * @CreateTime 2024年05月09日 17:18:47
 */
@Data
@ApiModel(description = "服务号模板实际发送的信息")
public class WxMpTemplateDataDTO implements Serializable {
    @ApiModelProperty(value = "模板发送的数据名称",required = true)
    @NotNull(message = "模板发送的数据名称不能为空!")
    private String name;
    @ApiModelProperty(value = "模板发送的数据值",required = true)
    @NotNull(message = "模板发送的数据值不能为空!")
    private String value;
    @ApiModelProperty(value = "模板发送的数据颜色")
    private String color;
    public WxMpTemplateDataDTO() {
    }

    public WxMpTemplateDataDTO(String name, String value) {
        this.name = name;
        this.value = value;
    }
    public WxMpTemplateDataDTO(String name, String value,String color) {
        this.name = name;
        this.value = value;
        this.color = color;
    }
}
