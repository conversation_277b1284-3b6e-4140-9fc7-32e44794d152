package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/22 17:17
 * @Description Saas平台下企业的banner搜索参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "Saas平台下企业的banner搜索参数", description = "Saas平台下企业的banner搜索参数")
public class SaasCompanyBannerSearchDTO  implements Serializable {

    @ApiModelProperty(value = "企业id",dataType = "Integer")
    @NotNull(message = "企业id不能为空")
    private Integer companyId;

}
