package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class LiveRoomInfoSendMqttDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("播放次数")
    private Integer studyUserTotal;

    @ApiModelProperty("点赞数量")
    private Long likeTotal;

    @ApiModelProperty("课件 id")
    private Integer courseTimetableId;

}
