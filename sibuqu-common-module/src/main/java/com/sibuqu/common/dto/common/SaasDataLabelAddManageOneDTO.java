package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 一个企业管理员添加到所有标签中
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaasDataLabelAddManageOneDTO implements Serializable {

    @ApiModelProperty(value = "企业ID",required = true)
    private Integer companyId;

    @ApiModelProperty(value = "用户Id",required = true)
    private Integer userId;
    @ApiModelProperty(value = "用户名字",required = true)
    private String userName;

    @ApiModelProperty(value = "创建人ID",required = true)
    private Integer createUserId;

    @ApiModelProperty(value = "创建人姓名",required = true)
    private String createUserName;

    @ApiModelProperty(value = "用户手机号",required = true)
    private String phone;
}
