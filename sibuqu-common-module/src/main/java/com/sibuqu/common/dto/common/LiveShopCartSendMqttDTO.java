package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class LiveShopCartSendMqttDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("操作类型 添加购物包商品-addLiveShopCart 删除购物包商品-removeLiveShopCart 推荐购物包商品-recommendLiveShopCart 清空购物包商品-clearLiveShopCart")
    private String oper;

    @ApiModelProperty("购物包商品类型（1-课程商品  2-外链商品）")
    private Integer shopBagGoodsType;

    @ApiModelProperty("课件 id")
    private Long courseTimetableId;

    @ApiModelProperty("商品数量")
    private Integer goodsCount;

    @ApiModelProperty("商品ID")
    private Long goodsId;

    @ApiModelProperty("商品外链地址")
    private String goodsLinkAddr;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品价格")
    private Long salePrice;

    @ApiModelProperty("商品缩略图")
    private String goodsSmallImg;

    @ApiModelProperty("商品悬浮时长 单位：秒")
    private Long suspendTime;

}
