package com.sibuqu.common.dto.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class WeekWorkListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "每页数量")
    private Integer pageSize;

    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @ApiModelProperty("部落 id")
    private Long tribeId;

    @ApiModelProperty(value = "是否在部落主页 0-否 1-是")
    private Integer tribeHomeFlag;

    @ApiModelProperty(value = "type 1-最热 2-最新")
    private Integer type;

    @ApiModelProperty(value = "是否只看自己的作业 1-是 0-否")
    private Integer myWork;

}