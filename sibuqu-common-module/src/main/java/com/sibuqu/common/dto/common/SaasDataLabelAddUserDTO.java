package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description SaaS后台数据标签批量添加人员DTO
 * @createTime 2022年5月31日14:41:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaasDataLabelAddUserDTO implements Serializable {

    @ApiModelProperty("数据标签ID")
    @NotNull(message = "数据标签ID不能为空")
    private Integer dataLabelId;

    @ApiModelProperty("用户Ids")
    @NotNull(message = "用户Ids不能为空")
    private List<Integer> userIds;

}
