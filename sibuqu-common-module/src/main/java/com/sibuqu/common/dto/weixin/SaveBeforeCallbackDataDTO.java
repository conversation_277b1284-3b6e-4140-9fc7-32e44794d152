package com.sibuqu.common.dto.weixin;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SaveBeforeCallbackDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "四部曲服务回调类型 1-小程序学习会创建编辑")
    private Integer dataType;

    @ApiModelProperty(value = "数据 id")
    private Long dataId;

    @ApiModelProperty(value = "任务 id，唯一请求标识，标记单次请求，用于匹配异步推送结果")
    private String traceId;

}
