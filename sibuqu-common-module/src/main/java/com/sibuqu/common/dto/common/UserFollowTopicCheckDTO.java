package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class UserFollowTopicCheckDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "话题ID")
    @NotNull(message = "话题ID不能为空")
    private Long topicId;

}