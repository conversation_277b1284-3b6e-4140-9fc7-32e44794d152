package com.sibuqu.common.dto.weixin;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName WxTemplateDataJsonVO.java
 * <AUTHOR>
 * @Version 1.0.0
 * @Description 模板设置模板信息
 * @CreateTime 2024年05月11日 15:44:57
 */
@Data
@ApiModel( description = "微信下的配置的所有模板消息列表")
public class WxTemplateDataJsonDTO implements Serializable {

    @ApiModelProperty(value = "模板发送的数据名称",required = true)
    @NotNull(message = "模板发送的数据名称不能为空!")
    private String name;
    @ApiModelProperty(value = "模板发送的数据值",required = true)
    @NotNull(message = "模板发送的数据值不能为空!")
    private String value;
    @ApiModelProperty(value = "模板发送的数据颜色")
    private String color;

    @ApiModelProperty(value = "备注说明",required = false)
    private String remarks;

    @ApiModelProperty(value = "备注说明的特殊说明",required = false)
    private String remarksSpecial;

}
