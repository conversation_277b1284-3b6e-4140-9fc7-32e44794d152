package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/10/22 11:28
 * @Description 搜索账号信息参数
 */
@Data
public class SysUserSearchDTO {

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "账号", required = false, dataType = "String")
    private String personName;

    @ApiModelProperty(value = "手机号", required = false, dataType = "String")
    private String phone;

    @ApiModelProperty(value = "账号状态 0:禁用 1：启用", required = false, dataType = "String")
    private String state;

    @ApiModelProperty(value = "第几页", required = false, dataType = "int")
    private int pageNum;

    @ApiModelProperty(value ="分页大小",required = false, dataType = "int")
    private int pageSize;
}
