package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2022/9/14 10:30
 * @Version 1.0
 * @Description 直播评论管理查询参数
 **/
@Data
public class CommentLiveByCourseSearchDTO {
    @ApiModelProperty(value = "第几页")
    private int pageNum;

    @ApiModelProperty("分页大小")
    private int pageSize;


    @ApiModelProperty(value = "课程ID",required = true)
    @NotNull(message = "课程id不能为空")
    private Integer courseId;


    @ApiModelProperty(value = "关联课程时间表ID courseTimetableId",required = false)
    private Integer  courseTimetableId;

    @ApiModelProperty(value = "评论的内容")
    private String content;

    @ApiModelProperty("评论用户名称")
    private String  userName;

/*    @ApiModelProperty(value = "用户ID")
    private Integer userId;*/

    @ApiModelProperty("评论用户手机号")
    private String userPhone;
/*    @ApiModelProperty(value = "状态(0:隐藏1:显示)")
    private Integer  show;*/
    @ApiModelProperty("显示状态（0.隐藏 1.显示）")
    private Integer dataFlag;

    @ApiModelProperty("评论开始日期")
    private String beginDate;

    @ApiModelProperty("评论结束日期")
    private String endDate;

   /* @ApiModelProperty(value = "发布时间开始时间")
    private String  startDate;

    @ApiModelProperty(value = "发布时间结束时间")
    private String  endDate;*/

    @ApiModelProperty("后台审核状态(1.待审核 2.审核通过 3. 审核不通过)")
    private Integer backCheckStatus;

   /* @ApiModelProperty(value = "审核状态(0:AI自动判断未通过，1AI自动判断通过，2：人工审核未通过,3:人工审核通过)")
    private List<Integer> checkStatus;*/
   @ApiModelProperty("企业名称")
   private String companyName;

    @ApiModelProperty("企业下的用户名称")
    private String  companyUserName;
}
