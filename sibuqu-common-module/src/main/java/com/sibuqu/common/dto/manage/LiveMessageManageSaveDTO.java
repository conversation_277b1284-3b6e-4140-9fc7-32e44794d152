package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/8/26-15:23
 * 直播老师回复 图片 文本 列表信息
 */

@Data
public class LiveMessageManageSaveDTO {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id",required =true,dataType = "Integer")
    private Integer id;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id",required = true,dataType = "Integer")
    private Integer courseId;

    @ApiModelProperty(value ="关联课程时间表ID",required = true,dataType = "Integer")
    @NotNull
    private Integer courseTimetableId;

    /**
     * 消息类型，0：文本 1.图片 3.图片和文字 4.公告消息
     */
    @ApiModelProperty(value = "消息类型，0：文本 1.图片 3.图片和文字 4.公告消息",required = true,dataType = "Integer")
    @NotNull
    private Integer replyType;
    /**
     * 直播回复的内容
     */
    @ApiModelProperty(value = "直播回复的内容",required = false,dataType = "String")
    private String postContent;
    /**
     * 是否已发送 0：否 1：是
     */
    @ApiModelProperty(value = "是否已发送 0:否 1:是",required = true,dataType = "Integer")
    @NotNull
    @Min(0)
    @Max(1)
    private Integer isSend;
    /**
     * 消息类型是 图片消息，图片的地址
     */
    @ApiModelProperty(value = "消息类型是 图片消息，图片的地址",required = false,dataType = "String")
    private String imageUrl;

    /**
     * 是否置顶
     */
    @ApiModelProperty(value = "是否置顶 0：否，1：是",required = false,dataType = "Integer")
    private Integer isTop;

    /**
     * 跳转类型： 1 不跳转；2商品；3 h5链接；4 图片,5课件
     */
    @ApiModelProperty(value ="跳转类型： 1 不跳转；2商品；3 h5链接；4 图片,5课件",required = false,dataType = "Integer")
    private Integer gotoType;
    /**
     * 跳转类型- 对应的值
     */
    @ApiModelProperty(value ="跳转类型- 对应的值",required = false,dataType = "String")
    private String gotoVal;

    /**
     * 置顶时长 秒
     */
    @ApiModelProperty(value ="置顶时长 秒",required = false,dataType = "Integer")
    private Integer topSecond;
}
