package com.sibuqu.common.dto.common;

import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 课程或班级分享图 新增或修改
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel( description = "课程或班级分享图搜索列表")
public class CoursePosterShareSearchPageDTO extends PageBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty( value ="课程ID", required = true, dataType = "Integer")
    @NotNull(message = "课程ID不能为空")
    private Integer courseId;
}
