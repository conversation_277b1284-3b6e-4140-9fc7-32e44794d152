package com.sibuqu.common.dto.content;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TipOffContentDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关联数据类型 1-课程下班级内容 2-广场 3-专区 4-inno周作业")
    private Integer dataType;

    @ApiModelProperty(value = "数据id 2-内容id")
    private Long dataId;

    @ApiModelProperty(value = "举报类型 1-色情低俗 2-政治敏感 3-涉嫌欺诈 4-种族歧视 5-广告 6-其他")
    private Integer tipOffType;

    @ApiModelProperty(value = "举报内容")
    private String content;

}