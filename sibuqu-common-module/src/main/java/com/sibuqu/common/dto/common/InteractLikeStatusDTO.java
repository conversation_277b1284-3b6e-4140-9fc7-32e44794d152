package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/25 9:48
 * @Description 用户点赞状态查询接口参数
 */
@Data
public class InteractLikeStatusDTO implements Serializable {

    @ApiModelProperty(value = "平台类型（0.企业版 1.个人版）")
    private Integer platformType = 0;

    @ApiModelProperty(value = "数据ID,心得点赞对应作业id,课件评论点赞对应评论id,课件资源点赞对应课件id")
    @NotNull(message = "数据ID不能为空")
    private Integer dataId;

    @ApiModelProperty(value = "点赞类型 1-心得点赞 2-课件评论点赞 3-课件资源点赞 4-成长专区点赞 5-小程序心得点赞 6-小程序视频点赞 7-小程序会议点赞 11-小组文字点赞 12-小组视频点赞 13-小程序会议问题 14-家庭幸福暑期活动 15-小组共读 16-家书 17-成长案例 18-知行卡践行 19-小程序打卡 20-共读任务 21-共读任务记录 22-想法 23-十家连心周报 24-润泽头条音频点赞 25-橐龠会议内容 26-企业主页 27-用户学习排行榜 28-成长计划心得 29-课程笔记 30-公共内容 31-信箱 32-公共小视频 33-评论 34-回复")
    @NotNull(message = "点赞类型不能为空")
    private Integer likeType;

}
