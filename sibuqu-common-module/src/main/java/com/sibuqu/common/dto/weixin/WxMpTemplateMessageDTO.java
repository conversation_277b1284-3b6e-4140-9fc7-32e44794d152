package com.sibuqu.common.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * 发送服务号模板消息
 * @ClassName WxMpTemplateMessageDTO.java
 * <AUTHOR>
 * @Version 1.0.0
 * @Description TODO
 * @CreateTime 2024年05月09日 17:05:39
 */
@Data
@ApiModel(description = "服务号发送会议消息")
public class WxMpTemplateMessageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "接收人的openId",required = true)
    @NotNull(message = "接收人的openId不能为空")
    private String openId;



    @ApiModelProperty(value = "模板Id",required = true)
    private String templateId;

    @ApiModelProperty(value = "跳转的地址 优先级高于小程序跳转")
    private String url;

/*
    private String appId;


    private String pagePath;*/

    @ApiModelProperty(value = "模板发送的数据集合",required = true)
    @NotEmpty(message = "发送的模板消息不能为空")
    private List<WxMpTemplateDataDTO> data;
    @ApiModelProperty(value = "小程序发送的配置")
    private MiniProgram miniProgram;


    @ApiModelProperty(value = "业务使用,接收人的userId",required = false)
    private Integer userId;

    @ApiModelProperty(value = "业务使用,备注信息",required = false)
    private String remark;
    @Data
    public static class MiniProgram implements Serializable {
        private static final long serialVersionUID = -7945254706501974849L;
        @ApiModelProperty(value = "小程序的appId")
        private String appid;
        @ApiModelProperty(value = "小程序的跳转的页面")
        private String pagePath;
       // private boolean usePath = false;
    }
}
