package com.sibuqu.common.dto.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/6/20-7:13 下午
 * 禁言
 */
@Data
public class LiveMuteDTO {
    /**
     * 用户的userId
     */
    @ApiModelProperty(value = "用户的userId,uid",required = true,dataType = "Integer")
    @NotNull(message="用户id不能为空")
    private Integer userId;
    /**
     * 直播的房间号id
     */
    @ApiModelProperty(value ="直播间的id 课程时间表id",required = true)
    @NotNull(message="课程时间表id不能为空")
    private Integer courseTimetableId;
    /**
     * 禁言类型 0 本次禁言,1全部禁言
     */
    @ApiModelProperty(value = "禁言类型 0 本次禁言,1全部禁言",required = true,dataType = "Integer")
    @NotNull(message="禁言类型不能为空")
    private Integer speakType;
}
