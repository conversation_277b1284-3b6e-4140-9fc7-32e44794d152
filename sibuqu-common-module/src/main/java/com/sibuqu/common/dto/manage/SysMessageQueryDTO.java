package com.sibuqu.common.dto.manage;

import com.sibuqu.common.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *消息推送下的查询条件
 * <AUTHOR>
 */
@Data
public class SysMessageQueryDTO extends PageBase implements Serializable {
    /**
     * 发送人名字
     */
    @ApiModelProperty(value = "发送人名字",required = false,dataType = "String")
    private String sendUserName;
    /**
     * 消息标题
     */
    @ApiModelProperty(value = "消息标题",required = false,dataType = "String")
    private String title;
    /**
     * 发布类型0立即发布1定时发布2不发布
     */
    @ApiModelProperty(value = "发布类型0立即发布1定时发布2不发布",required = false,dataType = "Integer")
    private Integer publishType;
}
