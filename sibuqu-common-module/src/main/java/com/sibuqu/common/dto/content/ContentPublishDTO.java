package com.sibuqu.common.dto.content;

import com.sibuqu.common.bo.ContentBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ContentPublishDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关联数据类型 1-课程下班级内容 2-广场 3-专区 4-inno周作业")
    private Integer dataType;

    @ApiModelProperty(value = "关联数据id")
    private Long dataId;

    @ApiModelProperty(value = "关联子数据id")
    private Long subDataId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "内容列表")
    private List<ContentBO> contentList;

    @ApiModelProperty(value = "展示范围 1-公开 2-当前范围")
    private Integer showLimit;

    @ApiModelProperty(value = "话题id列表")
    private List<Long> topicIdList;

    @ApiModelProperty(value = "话题列表")
    private List<ContentPublishTopicDTO> topicList;

    @ApiModelProperty(value = "内容定时发布时间")
    private LocalDateTime beginTime;
}