package com.sibuqu.common.dto.common;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/22 17:17
 * @Description Saas平台下企业的banner编辑
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "Saas平台下企业的banner编辑参数", description = "Saas平台下企业的banner编辑参数")
public class SaasCompanyBannerEditInfoDTO implements Serializable {

    @ApiModelProperty(value = "当前操作的userid",dataType = "Integer",required = true)
    @NotNull(message = "当前操作的userid不能为空")
    private Integer operatorUserId;

    @ApiModelProperty(value = "企业ID",dataType = "Integer",required = true)
    private Integer companyId;

    @ApiModelProperty(value = "企业的banner信息编辑数据",dataType = "List<SaasCompanyBannerEditInfoDTO>",required = true)
    private List<SaasCompanyBannerEditDTO> saasCompanyBannerEditDTOList;


}
