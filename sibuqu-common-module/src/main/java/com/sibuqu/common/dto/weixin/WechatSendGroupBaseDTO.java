package com.sibuqu.common.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 微信下模板消息的发送群组 查询
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Data
@Accessors(chain = true)
@ApiModel( description = "微信下模板消息的发送群组Id")
public class WechatSendGroupBaseDTO implements Serializable {

    @ApiModelProperty(value = "主键",required = true)
    @NotNull(message = "主键不能为空!")
    private Integer id;

}
