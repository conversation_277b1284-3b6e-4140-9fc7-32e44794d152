package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/25 9:48
 * @Description 发布互动评论入参
 */
@Data
public class InteractCommentAddDTO implements Serializable {

    @ApiModelProperty(value = "平台类型（0.企业版 1.个人版）",notes = "平台类型（0.企业版 1.个人版）",required = true)
    private Integer platformType = 0;

    @ApiModelProperty(value = "班级ID",notes = "班级ID",required = true)
    @NotNull(message = "班级ID不能为空")
    private Integer classesId;

    @ApiModelProperty(value = "发布类型（1.评论 2.回复）")
    @NotNull(message = "发布类型不能为空")
    private Integer sendType;

    @ApiModelProperty(value = "评论类型 1-心得评论 2-课件评论 3-成长专区评论 4-小程序心得评论 5-小程序视频评论 6-小程序会议评论 11-小组文字评论 12-小组视频评论 13-小程序会议问题 14-家庭幸福暑期活动 15-小组共读 16-家书 17-成长案例 19-小程序打卡 20-共读任务 21-共读任务记录 22-想法 23-十家连心周报 25-橐龠会议内容 28-成长计划心得 29-课程笔记 30-公共内容 31-信箱")
    @NotNull(message = "评论类型不能为空")
    private Integer commentType;

    @ApiModelProperty(value = "数据ID,心得评论对应作业id,课件资源点赞对应课件id")
    @NotNull(message = "数据ID不能为空")
    private Integer dataId;

    @ApiModelProperty(value = "内容",notes = "内容",required = true)
    @NotNull(message = "内容不能为空")
    private String content;

    @ApiModelProperty(value = "父级评论ID（回复请求场景下必传）",notes = "父级评论ID（回复请求场景下必传）")
    private Integer parentId;

    @ApiModelProperty(value = "是否是回复别人的回复（回复请求场景下必传 1是 0否）",notes = "是否是回复别人的回复（回复请求场景下必传 1是 0否）")
    private Integer isMoreLevelReply;

    @ApiModelProperty("评论用户ID")
    private Integer userId;

    @ApiModelProperty("评论用户姓名")
    private String userName;

    @ApiModelProperty("评论用户头像")
    private String userAvatar;

    @ApiModelProperty("评论用户手机号")
    private String userPhone;

    @ApiModelProperty("商品ID（因班级服务未重构，用于消息通知跳转参数，app访问班级首页）")
    private Integer skuId;

    @ApiModelProperty("来源类型（1.后台管理系统 2.APP）")
    private Integer sourceType;

    @ApiModelProperty(value="企业 id")
    private Integer companyId;

    @ApiModelProperty(value = "省编码")
    private String areaCode;

    @ApiModelProperty("课程id")
    private Integer courseId;

}
