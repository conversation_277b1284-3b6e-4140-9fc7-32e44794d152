package com.sibuqu.common.dto.common;

import com.sibuqu.common.enums.DictTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName DictionaryEntitySearchDTO.java
 * <AUTHOR>
 * @Version 1.0.0
 * @Description TODO 查询字典的信息
 * @CreateTime 2022年12月06日 10:55:50
 */
@Data
public class DictionaryEntitySearchDTO  {
    @ApiModelProperty(value = "字典类型（1课程编码 2企业行业 3热搜关键词 4帮助中心地址 5企业标签 10年龄，11性别，12职位，13企业员工数，14营业额 15人生阶段 16 是否为独生子 17 是否有宝宝 18 婚姻状况 19学历 20在职时长 21团队人数）",required = true)
    @NotNull
    private DictTypeEnum dictTypeEnum;

    @ApiModelProperty(value = "字典值 集合信息",required = false)
    private List<String> dictValues;

    @ApiModelProperty(value = "字典名称集合信息",required = false)
    private List<String> dictNames;
}
