package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class UpdateMessageSwitchDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "评论和回复消息开关")
    private Integer commentReplySwitch;

    @ApiModelProperty(value = "点赞消息总开关")
    private Integer likeSwitch;

    @ApiModelProperty(value = "学习提醒消息总开关")
    private Integer studyRemindSwitch;

    @ApiModelProperty(value = "订单消息开关")
    private Integer orderSwitch;

    @ApiModelProperty(value = "会议提醒开关")
    private Integer meetingRemindSwitch;

}
