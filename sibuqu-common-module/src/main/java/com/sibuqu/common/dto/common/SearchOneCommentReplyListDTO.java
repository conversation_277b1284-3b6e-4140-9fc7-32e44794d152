package com.sibuqu.common.dto.common;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SearchOneCommentReplyListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("评论ID")
    @TableField("comment_id")
    private Integer commentId;

    @ApiModelProperty("平台类型（0.企业版 1.个人版）")
    @TableField("platform_type")
    private Integer platformType;

    @ApiModelProperty(value = "页码")
    private int pageNum;

    @ApiModelProperty("分页大小")
    private int pageSize;
}
