package com.sibuqu.common.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class UpdateTempCommentIdDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "pageSize")
    private Integer pageSize;

    @ApiModelProperty(value = "只传3或者4     3：评论和回复消息 4：点赞消息 ")
    private Integer pushType;

}
