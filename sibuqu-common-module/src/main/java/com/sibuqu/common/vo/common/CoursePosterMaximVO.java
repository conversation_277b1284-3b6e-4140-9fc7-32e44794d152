package com.sibuqu.common.vo.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/10/27 7:59
 * @Description 课程分享海报箴言返回的参数信息
 */
@Data
@ApiModel(value = "课程分享海报箴言返回的参数信息")
public class CoursePosterMaximVO {

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty(value = "课程ID",required = false,dataType = "Integer")
    private Integer courseId;

    @ApiModelProperty(value = "海报箴言",required = false,dataType = "String")
    private String maxim;

    @ApiModelProperty(value = "出处",required = false,dataType = "String")
    private String source;

    @ApiModelProperty("海报正面图")
    private String posterFrontImg;

    @ApiModelProperty("海报背面图")
    private String posterVersoImg;
}
