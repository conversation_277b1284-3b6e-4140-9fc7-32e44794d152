package com.sibuqu.common.util;

/**
 * <AUTHOR>
 * @date 2021/5/13
 */
public class FormatUtil {
    /**
     * 将数值分数转换未SABC评级
     * <AUTHOR>
     * @date 2021/5/13
     * @param score:
     **/
    public static String scoreFormatString(Integer score){
        if (score==null || score==-1){
            return null;
        }
        if (score>90){
            return "S";
        }else if (score>70){
            return "A";
        }else if (score>60){
            return "B";
        }
        return "C";
    }
}
