package com.sibuqu.common.util;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/1 14:53
 * @description：
 * @modified By：
 * @version: $
 */
public class MathUtil {

    /**
     * byte(字节)根据长度转成mb(兆字节)
     *
     * @param bytes
     * @return
     */
    public static float bytes2mb(long bytes) {
        BigDecimal filesize = new BigDecimal(bytes);
        BigDecimal kilobyte = new BigDecimal(1024 * 1024);
        float returnValue = filesize.divide(kilobyte, 0, BigDecimal.ROUND_UP)
                .floatValue();
        return returnValue;

//        public static String bytes2mb(long bytes) {
//            DecimalFormat format = new DecimalFormat("###");
//            double res = (bytes / (1024.0 * 1024.0));
//            return format.format(res);
//        }
    }




}
