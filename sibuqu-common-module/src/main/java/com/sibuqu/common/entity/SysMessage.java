package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 推送消息表
 */
@ApiModel(description = "推送消息表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_message")
public class SysMessage implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    @TableField(value = "title")
    @ApiModelProperty(value = "消息标题")
    private String title;

    @TableField(value = "content")
    @ApiModelProperty(value = "消息内容")
    private String content;

    @TableField(value = "app_source")
    @ApiModelProperty("app来源 1-润泽园app 2-战略创新app")
    private Integer appSource;

    @TableField(value = "push_type")
    @ApiModelProperty(value = "推送消息的类型：1-系统消息 2-活动消息 3-评论和回复消息 4-点赞消息 5-学习提醒 7-订单消息 8-会议消息")
    private Integer pushType;

    @TableField(value = "publish_type")
    @ApiModelProperty(value = "发布类型0立即发布1定时发布2不发布")
    private Integer publishType;

    @TableField(value = "send_status")
    @ApiModelProperty(value = "发送状态 0-未发送 1-已发送 2-发送中")
    private Integer sendStatus;

    @TableField(value = "publish_time")
    @ApiModelProperty(value = "发布时间")
    private LocalDateTime publishTime;

    @TableField(value = "all_flag")
    @ApiModelProperty(value = "消息是否发送给所有人 0:不是发送给所有人的消息 1:发送给所有人的消息")
    private Integer allFlag;

    @TableField(value = "back_show_flag")
    @ApiModelProperty(value = "是否在后台展示 0-不展示 1-展示")
    private Integer backShowFlag;

    @TableField(value = "send_type")
    @ApiModelProperty(value = "发送类型 1-所有人员 2-按照课程权限 3-指定用户")
    private Integer sendType;

    @TableField(value = "send_config")
    @ApiModelProperty(value = "发送消息配置")
    private String sendConfig;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除0未删除1已删除")
    private Integer deleteFlag;

    @TableField(value = "dictionary_one")
    @ApiModelProperty(value = "字典表1级")
    private String dictionaryOne;

    @TableField(value = "dictionary_two")
    @ApiModelProperty(value = "字典表2级")
    private String dictionaryTwo;

    @TableField(value = "dictionary_three")
    @ApiModelProperty(value = "字典表3级")
    private String dictionaryThree;

    @TableField(value = "sku_ids")
    @ApiModelProperty(value = "商品ids")
    private String skuIds;

    @TableField(value = "image_url")
    @ApiModelProperty(value = "列表图地址")
    private String imageUrl;

    @TableField(value = "action_type")
    @ApiModelProperty(value = "系统消息，活动消息-操作类型0不跳转1H5页面2商品3课件")
    private Integer actionType;

    @TableField(value = "action_info")
    @ApiModelProperty(value = "系统消息，活动消息-跳转的具体信息，H5页面存地址，商品存商品ID(sku_id)，课件存课件rel_id")
    private String actionInfo;

    @TableField(value = "action_title")
    @ApiModelProperty(value = "回显标题")
    private String actionTitle;

    @TableField(value = "head_url")
    @ApiModelProperty(value = "消息发送人头像地址")
    private String headUrl;

    @TableField(value = "jump_page")
    @ApiModelProperty(value = "跳转类型 1-新版本心得列表 2-老版本心得列表 3-课件评论回复列表 4-课件评论点赞列表 11-订单详情页 12-发票详情页 13-退款详情页 21-会议提醒 22-学员申请参会消息 23-学员会议请假消息 24-学员参会申请通过消息 25-专区学员对内容的评论 26-学员对@内容的评论 27-学员被@的消息 28-专区学员对内容的点赞 29-专区对内容的引用 30-企业版班级主页 31-十家连心页 32-用户学习排行榜 33-广场内容详情页 34-邀请加入小组")
    private Integer jumpPage;

    @TableField(value = "data_id")
    @ApiModelProperty(value = "数据ID")
    private Long dataId;

    @TableField(value = "pref_id")
    @ApiModelProperty(value = "专区id")
    private Integer prefId;

    @TableField(value = "content_id")
    @ApiModelProperty(value = "内容 id")
    private Integer contentId;

    @TableField(value = "province_code")
    @ApiModelProperty(value = "省编码")
    private String provinceCode;

    @TableField(value = "remind_type")
    @ApiModelProperty(value = "学习提醒-提醒类型 1-作业提醒 2-上课提醒 3-直播预约提醒 4-商品详情页")
    private Integer remindType;

    @TableField(value = "send_user_name")
    @ApiModelProperty(value = "消息发送人用户名")
    private String sendUserName;

    @TableField(value = "course_type")
    @ApiModelProperty(value = "课程类型")
    private Integer courseType;

    @TableField(value = "class_name")
    @ApiModelProperty(value = "班级名称")
    private String className;

    @TableField(value = "batch_id")
    @ApiModelProperty(value = "导入用户批次号")
    private String batchId;

    @TableField(value = "work_id")
    @ApiModelProperty(value = "心得id")
    private Integer workId;

    @TableField(value = "classes_id")
    @ApiModelProperty(value = "班级id")
    private Integer classesId;

    @TableField(value = "course_id")
    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @TableField(value = "course_timetable_id")
    @ApiModelProperty(value = "课件 id")
    private Integer courseTimetableId;

    @TableField(value = "comment_id")
    @ApiModelProperty(value = "评论id")
    private Integer commentId;

    @TableField(value = "resource_id")
    @ApiModelProperty(value = "资源id")
    private Integer resourceId;

    @TableField(value = "rel_Id")
    @ApiModelProperty(value = "课件id")
    private Integer relId;

    @TableField(value = "sku_id")
    @ApiModelProperty(value = "商品id")
    private Integer skuId;

    @TableField(value = "company_id")
    @ApiModelProperty(value = "企业id")
    private Integer companyId;

    @TableField(value = "page_url")
    @ApiModelProperty(value = "url地址")
    private String pageUrl;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人id")
    private Integer createUserId;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "操作人id")
    private Integer updateUserId;

    @TableField(value = "update_time")
    @ApiModelProperty(value = " 更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "send_user_total")
    @ApiModelProperty(value = "发送人数")
    private Integer sendUserTotal;

    private static final long serialVersionUID = 1L;
}