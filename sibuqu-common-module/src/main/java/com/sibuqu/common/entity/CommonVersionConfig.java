package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@ApiModel(description = "版本更新各设备配置")
@Data
@TableName(value = "common_version_config")
public class CommonVersionConfig implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "device_type")
    @ApiModelProperty(value = "设备类型 ANDROID IOS PC_M PC_W PC_HOT IM")
    private String deviceType;

    @TableField(value = "app_source")
    @ApiModelProperty("app来源 1-润泽园app 2-战略创新app")
    private Integer appSource;

    @TableField(value = "channel")
    @ApiModelProperty(value = "渠道 huawei oppo")
    private String channel;

    @TableField(value = "min_force_update")
    @ApiModelProperty(value = "最小强更版本")
    private Integer minForceUpdate;

    @TableField(value = "apple_switch")
    @ApiModelProperty(value = "游客模式开关 0-关 其他值-开游客的version_code")
    private Integer appleSwitch;

    @TableField(value = "grayscale_type")
    @ApiModelProperty(value = "灰度开关 0-关 其他值-开灰度的version_code")
    private Integer grayscaleType;

    @TableField(value = "mantle_switch")
    @ApiModelProperty(value = "蒙层开关 0-关 1-开")
    private Integer mantleSwitch;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人 id")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人 id")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}