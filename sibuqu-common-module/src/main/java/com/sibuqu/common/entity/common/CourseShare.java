package com.sibuqu.common.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品分享
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_course_share")
@ApiModel(value = "CourseShare对象", description = "课程分享")
public class CourseShare implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    private Integer id;

    /**
     * 企业ID
     */
    @TableField(value = "company_id")
    @ApiModelProperty(value="企业ID")
    private Integer companyId;

    /**
     * 课程ID
     */
    @TableField(value = "course_id")
    @ApiModelProperty(value="课程ID")
    private Integer courseId;

    /**
     * 商品ID
     */
    @TableField(value = "goods_id")
    @ApiModelProperty(value="商品ID")
    private Integer goodsId;

    /**
     * 海报导语
     */
    @TableField(value = "share_words")
    @ApiModelProperty(value="海报导语")
    private String shareWords;

    /**
     * 标题
     */
    @TableField(value = "title")
    @ApiModelProperty(value="标题")
    private String title;

    /**
     * 描述
     */
    @TableField(value = "desc_info")
    @ApiModelProperty(value="描述")
    private String descInfo;

    /**
     * 分享链接-分享图地址
     */
    @TableField(value = "share_map_url")
    @ApiModelProperty(value="分享链接-分享图地址")
    private String shareMapUrl;

    /**
     * 海报图地址
     */
    @TableField(value = "poster_url")
    @ApiModelProperty(value="海报图地址")
    private String posterUrl;

    /**
     * 课程详情链接
     */
    @TableField(value = "course_details_url")
    @ApiModelProperty(value="课程详情链接")
    private String courseDetailsUrl;

    /**
     * 课程详情二维码
     */
    @TableField(value = "course_qr_code_url")
    @ApiModelProperty(value="课程详情二维码")
    private String courseQrCodeUrl;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty(value="创建人id")
    private Integer createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 操作人id
     */
    @TableField(value = "update_user_id")
    @ApiModelProperty(value="操作人id")
    private Integer updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updateTime;

    /**
     * 加入班级的分享文字描述
     */
    @TableField(value = "join_classes_desc")
    @ApiModelProperty(value="加入班级的分享文字描述")
    private String joinClassesDesc;

    /**
     * 加入班级的分享的背景图颜色色值
     */
    @TableField(value = "join_classes_bgcolor")
    @ApiModelProperty(value="加入班级的分享的背景图颜色色值")
    private String joinClassesBgcolor;

    /**
     * 加入班级的分享的图片
     */
    @TableField(value = "join_classes_image")
    @ApiModelProperty(value="加入班级的分享的图片")
    private String joinClassesImage;

    /**
     * 加入班级的分享海报图
     */
    @TableField(value = "join_classes_poster")
    @ApiModelProperty(value="加入班级的分享海报图")
    private String joinClassesPoster;

    /**
     * 分享班级内容
     */
    @TableField(value = "share_classes_desc")
    @ApiModelProperty(value="分享班级内容")
    private String shareClassesDesc;

    /**
     * 分享班级标题
     */
    @TableField(value = "share_classes_title")
    @ApiModelProperty(value="分享班级标题")
    private String shareClassesTitle;

    /**
     * 分享班级封面图
     */
    @TableField(value = "share_classes_image")
    @ApiModelProperty(value="分享班级封面图")
    private String shareClassesImage;

    /**
     * 课程分享-海报导语
     */
    @TableField(value = "course_share_introduction")
    @ApiModelProperty(value="课程分享-海报导语")
    private String courseShareIntroduction;



}
