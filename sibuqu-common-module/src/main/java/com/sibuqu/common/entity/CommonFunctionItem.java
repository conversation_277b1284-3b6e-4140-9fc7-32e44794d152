package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 功能项
 */
@ApiModel(description = "功能项")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_function_item")
public class CommonFunctionItem implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "`name`")
    @ApiModelProperty(value = "名称")
    private String name;

    @TableField(value = "area_id")
    @ApiModelProperty(value = "功能区 id")
    private Long areaId;

    @TableField(value = "pic_url")
    @ApiModelProperty(value = "图片 url")
    private String picUrl;

    @TableField(value = "jump_type")
    @ApiModelProperty(value = "跳转类型 1-H5地址 2-读书 3-课件")
    private Integer jumpType;

    @TableField(value = "jump_data_id")
    @ApiModelProperty(value = "跳转数据id 可能为：课程 id")
    private Long jumpDataId;

    @TableField(value = "jump_sub_data_id")
    @ApiModelProperty(value = "跳转子数据id 可能为：课件 id")
    private Long jumpSubDataId;

    @TableField(value = "jump_url")
    @ApiModelProperty(value = "跳转链接")
    private String jumpUrl;

    @TableField(value = "sort_num")
    @ApiModelProperty(value = "排序 从小到大")
    private Integer sortNum;

    @TableField(value = "show_begin_time")
    @ApiModelProperty(value = "显示开始时间")
    private LocalDateTime showBeginTime;

    @TableField(value = "show_end_time")
    @ApiModelProperty(value = "显示开始时间")
    private LocalDateTime showEndTime;

    @TableField(value = "up_status")
    @ApiModelProperty(value = "上下架状态 1-上架 0-下架")
    private Integer upStatus;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人 id")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人 id")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}