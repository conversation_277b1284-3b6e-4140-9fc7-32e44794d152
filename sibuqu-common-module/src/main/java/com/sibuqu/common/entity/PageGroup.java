package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 页面组
 */
@ApiModel(description = "页面组")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "page_group")
public class PageGroup implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    @TableField(value = "title")
    @ApiModelProperty(value = "标题")
    private String title;

    @TableField(value = "description")
    @ApiModelProperty(value = "描述")
    private String description;

    @TableField(value = "weixin_share_desc")
    @ApiModelProperty(value = "微信分享描述")
    private String weixinShareDesc;

    @TableField(value = "weixin_share_pic_url")
    @ApiModelProperty(value = "微信分享图片 url")
    private String weixinSharePicUrl;

    @TableField(value = "search_flag")
    @ApiModelProperty(value = "首页显示搜索 0-不搜索 1-搜索")
    private Integer searchFlag;

    @TableField(value = "go_back_flag")
    @ApiModelProperty(value = "内容页显示返回首页 0-不显示 1-显示")
    private Integer goBackFlag;

    @TableField(value = "download_flag")
    @ApiModelProperty(value = "图片支持下载 0-不支持 1-支持")
    private Integer downloadFlag;

    @TableField(value = "column_share_flag")
    @ApiModelProperty(value = "支持栏目页面分享 1-支持 0-不支持")
    private Integer columnShareFlag;

    @TableField(value = "content_share_flag")
    @ApiModelProperty(value = "支持内容页面分享 1-支持 0-不支持")
    private Integer contentShareFlag;

    @TableField(value = "column_go_back_flag")
    @ApiModelProperty(value = "栏目页返回首页 1-显示 0-不显示")
    private Integer columnGoBackFlag;

    @TableField(value = "video_download_flag")
    @ApiModelProperty(value = "视频支持下载 0-不支持 1-支持")
    private Integer videoDownloadFlag;

    @TableField(value = "file_download_flag")
    @ApiModelProperty(value = "文件支持下载 0-不支持 1-支持")
    private Integer fileDownloadFlag;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人 id")
    private Integer createUserId;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人 id")
    private Integer updateUserId;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}