package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户内容表
 */
@ApiModel(description = "用户内容表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_content")
public class CommonContent implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "data_type")
    @ApiModelProperty(value = "关联数据类型 1-课程下班级内容 2-广场 3-专区 4-inno周作业")
    private Integer dataType;

    @TableField(value = "data_id")
    @ApiModelProperty(value = "关联数据id")
    private Long dataId;

    @TableField(value = "sub_data_id")
    @ApiModelProperty(value = "关联子数据id")
    private Long subDataId;

    @TableField(value = "content_type")
    @ApiModelProperty(value = "内容类型 1-视频 2-图片(包含纯文本) 3-语音")
    private Integer contentType;

    @TableField(value = "pic_flag")
    @ApiModelProperty(value = "是否有图 0-没有 1-有图")
    private Integer picFlag;

    @TableField(value = "title")
    @ApiModelProperty(value = "标题")
    private String title;

    @TableField(value = "content")
    @ApiModelProperty(value = "内容json")
    private String content;

    @TableField(value = "recommend_flag")
    @ApiModelProperty(value = "推荐标识 0-不推荐 1-推荐")
    private Integer recommendFlag;

    @TableField(value = "recommend_sort")
    @ApiModelProperty(value = "推荐序号 越小越靠前")
    private Integer recommendSort;

    @TableField(value = "top_flag")
    @ApiModelProperty(value = "置顶标识 0-不置顶 1-置顶")
    private Integer topFlag;

    @TableField(value = "top_time")
    @ApiModelProperty(value = "置顶时间")
    private LocalDateTime topTime;

    @TableField(value = "user_id")
    @ApiModelProperty(value = "内容关联用户id")
    private Integer userId;

    @TableField(value = "user_phone")
    @ApiModelProperty(value = "内容关联用户手机号")
    private String userPhone;

    @TableField(value = "user_name")
    @ApiModelProperty(value = "内容关联用户姓名")
    private String userName;

    @TableField(value = "show_limit")
    @ApiModelProperty(value = "展示范围 1-公开 2-当前范围")
    private Integer showLimit;

    @TableField(value = "show_status")
    @ApiModelProperty(value = "内容展示状态 0-不展示 1-展示")
    private Integer showStatus;

    @TableField(value = "check_status")
    @ApiModelProperty(value = "审核状态 0-待审核 1-审核中 2-审核通过 3-审核不通过")
    private Integer checkStatus;

    @TableField(value = "recommend_value")
    @ApiModelProperty(value = "推荐值")
    private Long recommendValue;

    @TableField(value = "excellent_flag")
    @ApiModelProperty(value = "推优标识 0-不推优 1-推优")
    private Integer excellentFlag;

    @TableField(value = "data_flag")
    @ApiModelProperty(value = "隐藏状态 1-已隐藏 0-未隐藏")
    private Integer dataFlag;

    @TableField(value = "begin_time")
    @ApiModelProperty(value = "内容定时发布时间")
    private LocalDateTime beginTime;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人ID")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "修改人ID")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    private @TableLogic Integer deleteFlag;
}