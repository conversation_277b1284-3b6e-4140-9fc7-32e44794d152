package com.sibuqu.common.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 课程对应的海报箴言列表信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_course_poster_maxim")
@ApiModel(value = "CoursePosterMaxim对象", description = "课程对应的海报箴言列表信息")
public class CoursePosterMaxim implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("课程ID")
    @TableField("course_id")
    private Integer courseId;

    @ApiModelProperty("海报箴言")
    @TableField("maxim")
    private String maxim;

    @ApiModelProperty("出处")
    @TableField("source")
    private String source;

    @ApiModelProperty("箴言类型（1.海报箴言  2.经典撞击 3.天地常新金句 4.幸福家庭暑期活动金句 5.向阳明先生请教 6.战略app图片金句）")
    @TableField("type")
    private Integer type;

    @ApiModelProperty("海报正面图")
    @TableField("poster_front_img")
    private String posterFrontImg;

    @ApiModelProperty("海报背面图")
    @TableField("poster_verso_img")
    private String posterVersoImg;

    @ApiModelProperty("创建人id")
    @TableField("create_user_id")
    private String createUserId;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("操作人id")
    @TableField("update_user_id")
    private String updateUserId;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;


}
