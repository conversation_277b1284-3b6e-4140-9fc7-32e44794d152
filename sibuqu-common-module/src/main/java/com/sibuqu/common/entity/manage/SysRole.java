package com.sibuqu.common.entity.manage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统角色-基础信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("sys_role")
@ApiModel(value = "SysRole对象", description = "系统角色-基础信息")
public class SysRole implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("角色ID")
    @TableId(value = "role_id", type = IdType.AUTO)
    private Integer roleId;

    @ApiModelProperty("角色编码")
    @TableField("role_code")
    private String roleCode;

    @ApiModelProperty("角色名称")
    @TableField("role_name")
    private String roleName;

    @ApiModelProperty("状态:0-无效 1-有效")
    @TableField("status")
    private Integer status;

    @ApiModelProperty("角色描述")
    @TableField("role_desc")
    private String roleDesc;

    @ApiModelProperty("保留数据0-否 1-是 不允许删除")
    @TableField("is_persist")
    private Integer isPersist;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人_用户id")
    @TableField("create_user_id")
    private Integer createUserId;

    @ApiModelProperty("创建人名称")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人_用户id")
    @TableField("update_user_id")
    private Integer updateUserId;

    @ApiModelProperty("修改人")
    @TableField("update_user_name")
    private String updateUserName;


}
