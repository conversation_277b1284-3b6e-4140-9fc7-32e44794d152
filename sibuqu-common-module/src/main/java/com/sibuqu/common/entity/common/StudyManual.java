package com.sibuqu.common.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学习手册
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_study_manual")
@ApiModel(value = "StudyManual对象", description = "学习手册")
public class StudyManual implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("course_id")
    private Integer courseId;

    @ApiModelProperty("对应证书:title")
    @TableField("course_name")
    private String courseName;

    @TableField("user_id")
    private Integer userId;

    @TableField("user_name")
    private String userName;

    @ApiModelProperty("班级id")
    @TableField("class_group_id")
    private Integer classGroupId;

    @ApiModelProperty("班级名称")
    @TableField("class_name")
    private String className;

    @ApiModelProperty("学习的日子")
    @TableField("study_day")
    private Integer studyDay;

    @ApiModelProperty("学习人数")
    @TableField("study_number")
    private Double studyNumber;

    @ApiModelProperty("心得作业数")
    @TableField("homework_count")
    private Integer homeworkCount;

    @ApiModelProperty("有多少天的正课")
    @TableField("homework_total_count")
    private Integer homeworkTotalCount;

    @ApiModelProperty("听课总数")
    @TableField("listen_total_count")
    private Integer listenTotalCount;

    @ApiModelProperty("学分")
    @TableField("study_credit")
    private Integer studyCredit;

    @ApiModelProperty("排名")
    @TableField("user_rank")
    private Double userRank;

    @ApiModelProperty("总排名")
    @TableField("total_rank")
    private String totalRank;

    @ApiModelProperty("排名超过人数,带单位")
    @TableField("study_proportion")
    private String studyProportion;

    @ApiModelProperty("最早心得id")
    @TableField("min_exper_id")
    private Integer minExperId;

    @ApiModelProperty("最迟心得id")
    @TableField("max_exper_id")
    private Integer maxExperId;

    @ApiModelProperty("最早心得日期")
    @TableField("study_experience_start_time")
    private LocalDateTime studyExperienceStartTime;

    @ApiModelProperty("最迟心得日期")
    @TableField("study_experience_last_time")
    private LocalDateTime studyExperienceLastTime;

    @ApiModelProperty("第一篇心得")
    @TableField("study_experience")
    private String studyExperience;

    @ApiModelProperty("我的立志")
    @TableField("study_resolve")
    private String studyResolve;

    @ApiModelProperty("最后的心得")
    @TableField("study_experience_last")
    private String studyExperienceLast;

    @ApiModelProperty("心得数量-带单位（份）")
    @TableField("study_experience_numb")
    private String studyExperienceNumb;

    @ApiModelProperty("心得出现最多的5个词")
    @TableField("study_experience_most")
    private String studyExperienceMost;

    @ApiModelProperty("多少天不讲谎言（带单位 天）")
    @TableField("study_lie_day")
    private String studyLieDay;

    @ApiModelProperty("多少天不讲抱怨")
    @TableField("study_complain_day")
    private String studyComplainDay;

    @ApiModelProperty("课程类型 2阳明心学一期，101阳明心学二期200易经")
    @TableField("course_type")
    private String courseType;

    @TableField("study_start_time")
    private LocalDateTime studyStartTime;

    @TableField("study_end_time")
    private LocalDateTime studyEndTime;

    @TableField("study_start_time_long")
    private Long studyStartTimeLong;

    @TableField("study_end_time_long")
    private Long studyEndTimeLong;

    @TableField("graduate_time")
    private Long graduateTime;

    @TableField("cert_num")
    private String certNum;

    @ApiModelProperty("证书类型：0-a系列结业 1-a系列毕业；10-阳明一期普通，11阳明一期优秀，12阳明一期荣誉(全部学分)	")
    @TableField("type")
    private Integer type;

    @ApiModelProperty("1不显示0显示")
    @TableField("delete_flag")
    private Integer deleteFlag;


}
