package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 老师回复的消息信息-图片之类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_live_teacher_reply")
@ApiModel(value = "LiveTeacherReply对象", description = "老师回复的消息信息-图片之类")
public class LiveTeacherReply implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("课程id")
    @TableField("course_id")
    private Integer courseId;

/*    @ApiModelProperty("资源id")
    @TableField("res_id")
    private Integer resId;

    @ApiModelProperty("课程与资源的中间表id")
    @TableField("rel_id")
    private Integer relId;*/

    @ApiModelProperty(value = "资源id")
    @TableField("resource_id")
    private Integer resourceId;

    @ApiModelProperty("关联课程时间表ID")
    @TableField("course_timetable_id")
    private Integer courseTimetableId;

    @ApiModelProperty("消息类型，0：文本 1.图片 3.图文消息")
    @TableField("reply_type")
    private Integer replyType;

    @ApiModelProperty("直播回复的内容")
    @TableField("post_content")
    private String postContent;

   // @ApiModelProperty("用户楼层字典id")
   // @TableField("msg_type")
   // private Integer msgType;

   // @ApiModelProperty("课程类型200易经")
   // @TableField("course_type")
   // private Integer courseType;

    @ApiModelProperty("是否已发送 0：否 1：是")
    @TableField("is_send")
    private Integer isSend;

    @ApiModelProperty("用户id")
    @TableField("user_id")
    private Integer userId;

    @ApiModelProperty("消息类型是 图片消息，图片的地址")
    @TableField("image_url")
    private String imageUrl;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人名称")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人_用户id")
    @TableField("update_user_id")
    private Integer updateUserId;

    @ApiModelProperty("修改人名称")
    @TableField("update_user_name")
    private String updateUserName;

    @ApiModelProperty("是否置顶 0:否 1:是")
    @TableField("is_top")
    private Integer isTop;

    @ApiModelProperty("跳转类型： 1 不跳转；2商品；3 h5链接；4 图片,5课件")
    @TableField("goto_type")
    private Integer gotoType;

    @ApiModelProperty("跳转类型- 对应的值")
    @TableField("goto_val")
    private String gotoVal;

    @ApiModelProperty("是置顶,多少秒 默认3秒")
    @TableField("top_second")
    private Integer topSecond;

    @ApiModelProperty("0-有效；1-无效 删除")
    @TableField("delete_flag")
    @TableLogic
    private Integer deleteFlag;


}
