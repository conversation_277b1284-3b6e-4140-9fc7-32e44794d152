package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 短信模板配置信息
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("sms_config")
@ApiModel(value = "短信模板配置信息", description = "短信模板配置信息")
public class SmsConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键信息")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("name")
    @ApiModelProperty("短信模板名称")
    private String name;

    @TableField("sms_no")
    @ApiModelProperty("短信模板编号")
    private String smsNo;

    @TableField("type")
    @ApiModelProperty("类型 1-购课 2-兑换 3-退款")
    private String type;

    @TableField("content")
    @ApiModelProperty("短信模板内容")
    private String content;

    @TableField("remark")
    @ApiModelProperty("场景说明")
    private String remark;

    @TableField("data_flag")
    @ApiModelProperty("上下架状态 0-下架 1-上架")
    private Integer dataFlag;

    @TableField("create_user_id")
    @ApiModelProperty("创建人ID")
    private Integer createUserId;

    @TableField("create_user_name")
    @ApiModelProperty("创建人名称")
    private String createUserName;

    @TableField("create_time")
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @TableField("update_user_id")
    @ApiModelProperty("修改人ID")
    private Integer updateUserId;

    @TableField("update_user_name")
    @ApiModelProperty("修改人名称")
    private String updateUserName;

    @TableField("update_time")
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @TableField("delete_flag")
    @ApiModelProperty("删除状态 1-是 0-否")
    private Integer deleteFlag;

}
