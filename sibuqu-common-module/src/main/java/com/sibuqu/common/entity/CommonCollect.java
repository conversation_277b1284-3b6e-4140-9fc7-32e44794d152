package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 收藏
    */
@ApiModel(description="收藏")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_collect")
public class CommonCollect implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 关联数据类型 1-小程序视频 12-小组视频
     */
    @TableField(value = "data_type")
    @ApiModelProperty(value="关联数据类型 1-小程序视频 12-小组视频")
    private Integer dataType;

    /**
     * 关联数据id
     */
    @TableField(value = "data_id")
    @ApiModelProperty(value="关联数据id")
    private Long dataId;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    @ApiModelProperty(value="用户id")
    private Integer userId;

    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty(value="创建人ID")
    private Integer createUserId;

    /**
     * 创建人名称
     */
    @TableField(value = "create_user_name")
    @ApiModelProperty(value="创建人名称")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(value = "update_user_id")
    @ApiModelProperty(value="修改人ID")
    private Integer updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name")
    @ApiModelProperty(value="修改人名称")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="修改时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}