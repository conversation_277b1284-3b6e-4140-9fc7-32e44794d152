package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户的禁言列表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_live_mute")
@ApiModel(value = "LiveMute对象", description = "用户的禁言列表")
public class LiveMute implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键信息")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("禁言的用户id")
    @TableField("mute_user_id")
    private Integer muteUserId;

/*    @ApiModelProperty("直播间的id res表中的主键也就是res_id")
    @TableField("room_id")
    private Integer roomId;*/
    @ApiModelProperty("直播间的id 课程时间表id")
    private Integer courseTimetableId;

    @ApiModelProperty("0:本场禁言 1:全部禁言")
    @TableField("mute_type")
    private Integer muteType;

    @ApiModelProperty("课件主标题 course_res表，直播名称")
    @TableField("res_title")
    private String resTitle;

    @ApiModelProperty("创建人名称user_id")
    @TableField("create_user_id")
    private Integer createUserId;

    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人名称")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人名字")
    @TableField("update_user_name")
    private String updateUserName;

    @ApiModelProperty("修改人_用户id")
    @TableField("update_user_id")
    private String updateUserId;

    @ApiModelProperty("是否删除(0未删除，1已删除)")
    @TableField("delete_flag")
    @TableLogic
    private Integer deleteFlag;


}
