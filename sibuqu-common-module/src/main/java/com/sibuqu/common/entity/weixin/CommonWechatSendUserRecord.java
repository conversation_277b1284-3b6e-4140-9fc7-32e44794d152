package com.sibuqu.common.entity.weixin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 微信下模板消息的发送对象的记录信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("common_wechat_send_user_record")
@ApiModel(value = "CommonWechatSendUserRecord对象", description = "微信下模板消息的发送对象的记录信息")
public class CommonWechatSendUserRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("消息模板id")
    @TableField("msg_temp_id")
    private Integer msgTempId;

    @ApiModelProperty("服务号的消息模板id")
    @TableField("template_id")
    private String templateId;

    @ApiModelProperty("服务号对应的用户openId")
    @TableField("open_id")
    private String openId;

    @ApiModelProperty("用户id")
    @TableField("user_id")
    private Integer userId;

    @ApiModelProperty("发送组的主键")
    @TableField("send_group_id")
    private Integer sendGroupId;

    @ApiModelProperty("发送消息的json")
    @TableField("send_json")
    private String sendJson;

    @ApiModelProperty("发送失败的原因")
    @TableField("send_error")
    private String sendError;

    @ApiModelProperty("接收返回的json")
    @TableField("receive_json")
    private String receiveJson;

    @ApiModelProperty("删除状态（1是 0否）")
    @TableField("delete_flag")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人_用户id")
    @TableField("create_user_id")
    private Integer createUserId;

    @ApiModelProperty("创建人名称")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人_用户id")
    @TableField("update_user_id")
    private Integer updateUserId;

    @ApiModelProperty("修改人")
    @TableField("update_user_name")
    private String updateUserName;

    @ApiModelProperty("备注信息")
    @TableField("remarks")
    private String remarks;

    @ApiModelProperty("是否发送成功 0：未成功 1：已成功")
    @TableField("is_success")
    private Integer isSuccess;

    @ApiModelProperty("'发送成功的msgId'")
    @TableField("msg_id")
    private String msgId;

}
