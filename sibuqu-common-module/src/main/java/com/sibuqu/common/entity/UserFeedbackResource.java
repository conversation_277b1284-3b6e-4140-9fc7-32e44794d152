package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户意见反馈资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_user_feedback_resource")
@ApiModel(value = "UserFeedbackResource对象", description = "用户意见反馈资源表")
public class UserFeedbackResource implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("意见反馈主表")
    @TableField("feedback_id")
    private Integer feedbackId;

    @ApiModelProperty("意见反馈图片")
    @TableField("url")
    private String url;

    @ApiModelProperty("0-有效；1-无效")
    @TableField("delete_tag")
    private Integer deleteTag;

    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("0：代表用户反馈，1：代表老师回复")
    @TableField("type")
    private Integer type;

    @ApiModelProperty("修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;


}
