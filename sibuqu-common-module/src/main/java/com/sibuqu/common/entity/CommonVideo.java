package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 公共小视频表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_video")
@ApiModel(value = "CommonVideo对象", description = "公共小视频表")
public class CommonVideo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("关联数据类型 1-预告片")
    @TableField("data_type")
    private Integer dataType;

    @ApiModelProperty("关联数据id")
    @TableField("data_id")
    private Long dataId;

    @ApiModelProperty("标题")
    @TableField("title")
    private String title;

    @ApiModelProperty("封面图")
    @TableField("cover_url")
    private String coverUrl;

    @ApiModelProperty("描述")
    @TableField("video_desc")
    private String videoDesc;

    @ApiModelProperty("视频长度(如28:30,自动获取)")
    @TableField("video_time_length")
    private String videoTimeLength;

    @ApiModelProperty("视频大小(如102.91)")
    @TableField("video_size")
    private String videoSize;

    @ApiModelProperty("视频标题")
    @TableField("video_title")
    private String videoTitle;

    @ApiModelProperty("视频地址")
    @TableField("video_url")
    private String videoUrl;

    @ApiModelProperty("云视频id")
    @TableField("video_id")
    private String videoId;

    @ApiModelProperty("排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty("状态（1上架 2下架）")
    @TableField("data_flag")
    private Boolean dataFlag;

    @ApiModelProperty("创建人ID")
    @TableField("create_user_id")
    private Integer createUserId;

    @ApiModelProperty("创建人名称")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("修改人ID")
    @TableField("update_user_id")
    private Integer updateUserId;

    @ApiModelProperty("修改人名称")
    @TableField("update_user_name")
    private String updateUserName;

    @ApiModelProperty("修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("删除状态（1是 0否）")
    @TableField("delete_flag")
    @TableLogic
    private Boolean deleteFlag;


}
