package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 举报记录
 */
@ApiModel(description = "举报记录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_tip_off")
public class CommonTipOff implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "data_type")
    @ApiModelProperty(value = "关联数据类型 1-课程下班级内容 2-广场 3-专区 4-inno周作业")
    private Integer dataType;

    @TableField(value = "data_id")
    @ApiModelProperty(value = "数据 id")
    private Long dataId;

    @TableField(value = "tip_off_user_id")
    @ApiModelProperty(value = "举报人用户 id")
    private Integer tipOffUserId;

    @TableField(value = "tip_off_user_name")
    @ApiModelProperty(value = "举报人用户名称")
    private String tipOffUserName;

    @TableField(value = "be_tip_off_user_id")
    @ApiModelProperty(value = "被举报人用户 id")
    private Integer beTipOffUserId;

    @TableField(value = "be_tip_off_user_name")
    @ApiModelProperty(value = "被举报人用户名称")
    private String beTipOffUserName;

    @TableField(value = "tip_off_type")
    @ApiModelProperty(value = "举报类型 1-色情低俗 2-政治敏感 3-涉嫌欺诈 4-种族歧视 5-广告 6-其他")
    private Integer tipOffType;

    @TableField(value = "content")
    @ApiModelProperty(value = "举报内容")
    private String content;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人 id")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人 id")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    private Integer deleteFlag;
}