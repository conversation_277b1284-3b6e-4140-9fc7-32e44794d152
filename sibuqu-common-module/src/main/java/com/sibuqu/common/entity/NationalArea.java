package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 全国区域表
 */
@ApiModel(description = "全国区域表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "national_area")
public class NationalArea implements Serializable {
    /**
     * 区域ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "区域ID")
    private Integer id;

    /**
     * 上级区域ID
     */
    @TableField(value = "parent_id")
    @ApiModelProperty(value = "上级区域ID")
    private Integer parentId;

    /**
     * 行政区域等级 1-省 2-市 3-区县 4-街道镇
     */
    @TableField(value = "`level`")
    @ApiModelProperty(value = "行政区域等级 1-省 2-市 3-区县 4-街道镇")
    private Integer level;

    /**
     * 名称
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 排序
     */
    @TableField(value = "sort_num")
    @ApiModelProperty(value = "排序")
    private Integer sortNum;

    /**
     * 完整名称
     */
    @TableField(value = "whole_name")
    @ApiModelProperty(value = "完整名称")
    private String wholeName;

    /**
     * 本区域经度
     */
    @TableField(value = "lon")
    @ApiModelProperty(value = "本区域经度")
    private String lon;

    /**
     * 本区域维度
     */
    @TableField(value = "lat")
    @ApiModelProperty(value = "本区域维度")
    private String lat;

    /**
     * 电话区号
     */
    @TableField(value = "city_code")
    @ApiModelProperty(value = "电话区号")
    private String cityCode;

    /**
     * 邮政编码
     */
    @TableField(value = "zip_code")
    @ApiModelProperty(value = "邮政编码")
    private String zipCode;

    /**
     * 行政区划代码
     */
    @TableField(value = "area_code")
    @ApiModelProperty(value = "行政区划代码")
    private String areaCode;

    /**
     * 名称全拼
     */
    @TableField(value = "pin_yin")
    @ApiModelProperty(value = "名称全拼")
    private String pinYin;

    /**
     * 首字母简拼
     */
    @TableField(value = "simple_py")
    @ApiModelProperty(value = "首字母简拼")
    private String simplePy;

    /**
     * 区域名称拼音的第一个字母
     */
    @TableField(value = "per_pin_yin")
    @ApiModelProperty(value = "区域名称拼音的第一个字母")
    private String perPinYin;

    private static final long serialVersionUID = 1L;
}