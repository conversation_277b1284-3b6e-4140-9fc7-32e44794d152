package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@ApiModel(description = "common_flexible_page")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_flexible_page")
public class CommonFlexiblePage implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "program_id")
    @ApiModelProperty(value = "项目ID")
    private Long programId;

    @TableField(value = "page_title")
    @ApiModelProperty(value = "页面标题")
    private String pageTitle;

    @TableField(value = "page_desc")
    @ApiModelProperty(value = "页面描述")
    private String pageDesc;

    @TableField(value = "page_content")
    @ApiModelProperty(value = "页面内容")
    private String pageContent;

    @TableField(value = "page_setting")
    @ApiModelProperty(value = "页面设置")
    private String pageSetting;

    @TableField(value = "share_flag")
    @ApiModelProperty(value = "是否支持分享 0-不分享 1-分享")
    private Integer shareFlag;

    @TableField(value = "share_icon")
    @ApiModelProperty(value = "分享图标")
    private String shareIcon;

    @TableField(value = "bg_url")
    @ApiModelProperty(value = "背景图")
    private String bgUrl;

    @TableField(value = "bg_color")
    @ApiModelProperty(value = "背景色")
    private String bgColor;

    @TableField(value = "wx_share_title")
    @ApiModelProperty(value = "微信分享标题")
    private String wxShareTitle;

    @TableField(value = "wx_share_desc")
    @ApiModelProperty(value = "微信分享描述")
    private String wxShareDesc;

    @TableField(value = "wx_share_icon")
    @ApiModelProperty(value = "微信分享图标")
    private String wxShareIcon;

    @TableField(value = "open_flag")
    @ApiModelProperty(value = "页面是否开启 0-不开启 1-开启")
    private Integer openFlag;

    @TableField(value = "close_page_tip")
    @ApiModelProperty(value = "关闭页面提示")
    private String closePageTip;

    @TableField(value = "login_flag")
    @ApiModelProperty(value = "页面是否需要登录 0-不需要 1-需要")
    private Integer loginFlag;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人ID")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "修改人ID")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    private Integer deleteFlag;
}