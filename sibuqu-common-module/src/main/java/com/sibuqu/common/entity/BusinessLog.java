package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 业务日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_business_log")
@ApiModel(value = "BusinessLog对象", description = "业务日志表")
public class BusinessLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("服务CODE")
    @TableField("server_code")
    private String serverCode;

    @ApiModelProperty("服务名称")
    @TableField("server_name")
    private String serverName;

    @ApiModelProperty("功能CODE")
    @TableField("func_code")
    private String funcCode;

    @ApiModelProperty("功能名称")
    @TableField("func_name")
    private String funcName;

    @ApiModelProperty("关联数据ID")
    @TableField("data_id")
    private Integer dataId;

    @ApiModelProperty("关联数据名称")
    @TableField("data_name")
    private String dataName;

    @ApiModelProperty("操作人ID")
    @TableField("operate_user_id")
    private Integer operateUserId;

    @ApiModelProperty("操作人名称")
    @TableField("operate_user_name")
    private String operateUserName;

    @ApiModelProperty("操作时间")
    @TableField("operate_time")
    private LocalDateTime operateTime;

    @ApiModelProperty("操作类型")
    @TableField("operate_type")
    private String operateType;

    @ApiModelProperty("操作明细")
    @TableField("operate_detail")
    private String operateDetail;


}
