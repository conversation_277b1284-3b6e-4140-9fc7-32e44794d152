package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 中国行政地址信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_district")
@ApiModel(value = "District对象", description = "中国行政地址信息表")
public class District implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("唯一性编号")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("行政区划id")
    @TableField("city_code")
    private String cityCode;

    @ApiModelProperty("父级id")
    @TableField("parent_code")
    private String parentCode;

    @ApiModelProperty("行政区划全称")
    @TableField("name")
    private String name;

    @ApiModelProperty("省市区全称聚合")
    @TableField("merger_name")
    private String mergerName;

    @ApiModelProperty("行政区划简称")
    @TableField("short_name")
    private String shortName;

    @ApiModelProperty("行政区划简称聚合")
    @TableField("merger_short_name")
    private String mergerShortName;

    @ApiModelProperty("行政区划级别country:国家,province:省份,city:市,district:区县,street:街道")
    @TableField("level")
    private String level;

    @ApiModelProperty("级别 0.国家，1.省(直辖市) 2.市 3.区(县),4.街道")
    @TableField("level_type")
    private Integer levelType;

    @ApiModelProperty("电话区划号码")
    @TableField("telephone_code")
    private String telephoneCode;

    @ApiModelProperty("邮编")
    @TableField("zip_code")
    private String zipCode;

    @ApiModelProperty("拼音")
    @TableField("name_pinyin")
    private String namePinyin;

    @ApiModelProperty("简拼")
    @TableField("name_jianpin")
    private String nameJianpin;

    @ApiModelProperty("城市中心点")
    @TableField("name_first_char")
    private String nameFirstChar;

    @ApiModelProperty("首字母")
    @TableField("center")
    private String center;

    @ApiModelProperty("经度")
    @TableField("longitude")
    private String longitude;

    @ApiModelProperty("纬度")
    @TableField("latitude")
    private String latitude;

    @ApiModelProperty("状态 1可修改 2不可修改 3已删除")
    @TableField("status")
    private Integer status;

    @ApiModelProperty("历史版本")
    @TableField("version")
    @Version
    private String version;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;


}
