package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
    * 导入用户
    */
@ApiModel(value="导入用户")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "batch_user")
public class BatchUser implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    private Integer id;

    /**
     * 用户 id
     */
    @TableField(value = "user_id")
    @ApiModelProperty(value="用户 id")
    private Integer userId;

    /**
     * 批次 id
     */
    @TableField(value = "batch_id")
    @ApiModelProperty(value="批次 id")
    private String batchId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}