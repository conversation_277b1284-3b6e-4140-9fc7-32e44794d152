package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公共导入记录表
 */
@ApiModel(description = "公共导入记录表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_import_record")
public class CommonImportRecord implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    @TableField(value = "file_url")
    @ApiModelProperty(value = "文件链接地址")
    private String fileUrl;

    @TableField(value = "data_type")
    @ApiModelProperty(value = "数据类型 1-弹窗 2-广告位 3-版本更新人员")
    private Integer dataType;

    @TableField(value = "data_id")
    @ApiModelProperty(value = "数据id")
    private Integer dataId;

    @TableField(value = "import_count")
    @ApiModelProperty(value = "导入数量")
    private Integer importCount;

    @TableField(value = "success_count")
    @ApiModelProperty(value = "成功数量")
    private Integer successCount;

    @TableField(value = "fail_count")
    @ApiModelProperty(value = "失败数量")
    private Integer failCount;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人id")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "操作人id")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = " 更新时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}