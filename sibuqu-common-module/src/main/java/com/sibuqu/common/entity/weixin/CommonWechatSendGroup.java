package com.sibuqu.common.entity.weixin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 微信下模板消息的发送群组
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Data
@Accessors(chain = true)
@TableName("common_wechat_send_group")
@ApiModel(value = "CommonWechatSendGroup对象", description = "微信下模板消息的发送群组")
public class CommonWechatSendGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("消息模板id")
    @TableField("msg_temp_id")
    private Integer msgTempId;

//    @ApiModelProperty("发送对象 1:数据标签 2:课程")
//    @TableField("type")
//    private Integer type;

    @ApiModelProperty("发送对象的id 数据标签id，商品id")
    @TableField("data_id")
    private String dataId;

    @ApiModelProperty("发送对象的名称  数据标签名称，商品名称")
    @TableField("data_name")
    private String dataName;

    @ApiModelProperty("删除状态（1是 0否）")
    @TableField("delete_flag")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人_用户id")
    @TableField("create_user_id")
    private Integer createUserId;

    @ApiModelProperty("创建人名称")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人_用户id")
    @TableField("update_user_id")
    private Integer updateUserId;

    @ApiModelProperty("修改人")
    @TableField("update_user_name")
    private String updateUserName;

    @ApiModelProperty("备注信息")
    @TableField("remarks")
    private String remarks;


}
