package com.sibuqu.common.entity;

import lombok.Data;

/**
 * @ClassName Oauth2Token.java
 * <AUTHOR>
 * @Version 1.0.0
 * @Description TODO
 * @CreateTime 2024年04月17日 21:28:19
 */
@Data
public class Oauth2Token {
    /**
     * 网页授权接口调用凭证
     */
    private String accessToken;
    /**
     * 凭证有效时长
     */
    private int expiresIn;
    /**
     * 用于刷新凭证
     */
    private String refreshToken;
    /**
     * 用户标识
     */
    private String openId;
    /**
     * 用户授权作用域
     */
    private String scope;
}

