package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@ApiModel(description = "版本更新")
@Data
@TableName(value = "common_version")
public class CommonVersion implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "app_source")
    @ApiModelProperty("app来源 1-润泽园app 2-战略创新app")
    private Integer appSource;

    @TableField(value = "device_type")
    @ApiModelProperty(value = "设备类型 ANDROID IOS PC_M PC_W")
    private String deviceType;

    @TableField(value = "version_code")
    @ApiModelProperty(value = "版本号 1024")
    private Integer versionCode;

    @TableField(value = "version_name")
    @ApiModelProperty(value = "版本名称 V1.0.24")
    private String versionName;

    @TableField(value = "version_desc")
    @ApiModelProperty(value = "版本描述")
    private String versionDesc;

    @TableField(value = "status")
    @ApiModelProperty(value = "版本状态 0-无效 1-有效")
    private Integer status;

    @TableField(value = "url")
    @ApiModelProperty(value = "下载链接")
    private String url;

    @TableField(value = "open_time")
    @ApiModelProperty(value = "开启时间")
    private LocalDateTime openTime;

    @TableField(value = "update_status")
    @ApiModelProperty(value = "更新状态 0-无需更新 1-不提示更新 2-提示但不强更新 3-强制更新")
    private Integer updateStatus;

    @TableField(value = "frame_user_id")
    @ApiModelProperty(value = "0-全员 1-指定")
    private Integer frameUserId;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人 id")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人 id")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}