package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 页面组对应的模板
 */
@ApiModel(description = "页面组对应的模板")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "page_template")
public class PageTemplate implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    @TableField(value = "page_group_id")
    @ApiModelProperty(value = "页面组id")
    private Integer pageGroupId;

    @TableField(value = "template_config_id")
    @ApiModelProperty(value = "页面模板id")
    private Integer templateConfigId;

    @TableField(value = "page_type")
    @ApiModelProperty(value = "页面类型 1首页 2栏目页面 3内容页面 4独立页面")
    private Integer pageType;

    @TableField(value = "recommend_title")
    @ApiModelProperty(value = "推荐页面标题")
    private String recommendTitle;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人 id")
    private Integer createUserId;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人 id")
    private Integer updateUserId;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}