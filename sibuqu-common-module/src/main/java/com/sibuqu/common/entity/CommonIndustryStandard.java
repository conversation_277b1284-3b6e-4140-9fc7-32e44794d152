package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 所属行业企查查国标对应关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_industry_standard")
@ApiModel(value = "CommonIndustryStandard对象", description = "所属行业企查查国标对应关系表")
public class CommonIndustryStandard implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("润泽园一级行业ID")
    @TableField("industry_id")
    private Integer industryId;

    @ApiModelProperty("润泽园一级行业名称")
    @TableField("industry_name")
    private String industryName;

    @ApiModelProperty("润泽园二级行业ID")
    @TableField("second_industry_id")
    private Integer secondIndustryId;

    @ApiModelProperty("润泽园二级行业名称")
    @TableField("second_industry_name")
    private String secondIndustryName;

    @ApiModelProperty("企查查国标三级行业编码")
    @TableField("standard_code")
    private String standardCode;

    @ApiModelProperty("企查查国标三级行业名称")
    @TableField("standard_name")
    private String standardName;


}
