package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 页面组-独立页面
 */
@ApiModel(value = "页面组-独立页面")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "page_independent")
public class PageIndependent implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "independent_id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer independentId;

    /**
     * 标题
     */
    @TableField(value = "title")
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 页面类型 1:富文本&图片 2:带视频海报
     */
    @TableField(value = "independent_page_type")
    @ApiModelProperty(value = "页面类型 1:富文本&图片 2:带视频海报")
    private Integer independentPageType;

    /**
     * 页面id
     */
    @TableField(value = "page_single_id")
    @ApiModelProperty(value = "页面id")
    private Integer pageSingleId;

    /**
     * 背景图片（多个用逗号分割）
     */
    @TableField(value = "bg_url")
    @ApiModelProperty(value = "背景图片（多个用逗号分割）")
    private String bgUrl;

    /**
     * 页眉图片
     */
    @TableField(value = "header_url")
    @ApiModelProperty(value = "页眉图片")
    private String headerUrl;

    /**
     * 页面内容
     */
    @TableField(value = "content")
    @ApiModelProperty(value = "页面内容")
    private String content;

    /**
     * 页脚图片
     */
    @TableField(value = "footer_url")
    @ApiModelProperty(value = "页脚图片")
    private String footerUrl;

    /**
     * 视频封面图
     */
    @TableField(value = "video_cover_url")
    @ApiModelProperty(value = "视频封面图")
    private String videoCoverUrl;

    /**
     * 视频地址
     */
    @TableField(value = "video_url")
    @ApiModelProperty(value = "视频地址")
    private String videoUrl;

    /**
     * 直播标题
     */
    @TableField(value = "live_title")
    @ApiModelProperty(value = "直播标题")
    private String liveTitle;

    /**
     * 课程名称
     */
    @TableField(value = "course_name")
    @ApiModelProperty(value = "课程名称")
    private String courseName;

    /**
     * 是否显示按钮 0 不显示 1 显示
     */
    @TableField(value = "button_flag")
    @ApiModelProperty(value = "是否显示按钮 0 不显示 1 显示")
    private Boolean buttonFlag;

    /**
     * 按钮底色
     */
    @TableField(value = "button_bgcolor")
    @ApiModelProperty(value = "按钮底色")
    private String buttonBgcolor;

    /**
     * 按钮颜色
     */
    @TableField(value = "button_color")
    @ApiModelProperty(value = "按钮颜色")
    private String buttonColor;

    /**
     * 按钮文字
     */
    @TableField(value = "button_content")
    @ApiModelProperty(value = "按钮文字")
    private String buttonContent;

    /**
     * 按钮跳转类型 1:URL 2:课程详情页 3:课程购买支付页 4:兑换码购买支付页 5:独立直播 6:下载图片
     */
    @TableField(value = "button_jump_type")
    @ApiModelProperty(value = "按钮跳转类型 1:URL 2:课程详情页 3:课程购买支付页 4:兑换码购买支付页 5:独立直播 6:下载图片")
    private Integer buttonJumpType;

    /**
     * 课程id
     */
    @TableField(value = "course_id")
    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    /**
     * 课件id
     */
    @TableField(value = "rel_id")
    @ApiModelProperty(value = "课件id")
    private Integer relId;

    /**
     * 商品id
     */
    @TableField(value = "goods_id")
    @ApiModelProperty(value = "商品id")
    private Integer goodsId;

    /**
     * 按钮跳转url
     */
    @TableField(value = "button_jump_url")
    @ApiModelProperty(value = "按钮跳转url")
    private String buttonJumpUrl;

    /**
     * 微信分享展示标题
     */
    @TableField(value = "weixin_share_title")
    @ApiModelProperty(value = "微信分享展示标题")
    private String weixinShareTitle;

    /**
     * 微信分享描述
     */
    @TableField(value = "weixin_share_desc")
    @ApiModelProperty(value = "微信分享描述")
    private String weixinShareDesc;

    /**
     * 微信分享图片 url
     */
    @TableField(value = "weixin_share_pic_url")
    @ApiModelProperty(value = "微信分享图片 url")
    private String weixinSharePicUrl;

    /**
     * 创建人 id
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人 id")
    private Integer createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人 id
     */
    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人 id")
    private Integer updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}