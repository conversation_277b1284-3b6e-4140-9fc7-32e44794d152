package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 话题内容关联表
 */
@ApiModel(description = "话题内容关联表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_topic_rel")
public class CommonTopicRel implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "topic_id")
    @ApiModelProperty(value = "话题id")
    private Long topicId;

    @TableField(value = "content_id")
    @ApiModelProperty(value = "内容id")
    private Long contentId;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人id")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    @TableLogic
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}