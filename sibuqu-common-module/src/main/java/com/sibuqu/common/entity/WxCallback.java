package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 微信回调记录
    */
@ApiModel(description="微信回调记录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "wx_callback")
public class WxCallback implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 四部曲服务回调类型 1-小程序学习会创建编辑
     */
    @TableField(value = "data_type")
    @ApiModelProperty(value="四部曲服务回调类型 1-小程序学习会创建编辑")
    private Integer dataType;

    /**
     * 数据 id
     */
    @TableField(value = "data_id")
    @ApiModelProperty(value="数据 id")
    private Long dataId;

    /**
     * 任务 id，唯一请求标识，标记单次请求，用于匹配异步推送结果
     */
    @TableField(value = "trace_id")
    @ApiModelProperty(value="任务 id，唯一请求标识，标记单次请求，用于匹配异步推送结果")
    private String traceId;

    /**
     * 小程序的appid
     */
    @TableField(value = "appid")
    @ApiModelProperty(value="小程序的appid")
    private String appid;

    /**
     * 回调的所有数据
     */
    @TableField(value = "callback_data")
    @ApiModelProperty(value="回调的所有数据")
    private String callbackData;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}