package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;

/**
 * 全国区域表(NationalAreaInfo)表实体类
 *
 * <AUTHOR>
 * @since 2023-06-14 08:38:32
 */
@Data
public class NationalAreaInfo {

    //区域ID
    @TableId(type = IdType.AUTO)
    private Integer id;
    //行政区域等级 1-省 2-市 3-区县 4-街道镇
    private Integer level;
    //名称
    private String areaName;
    //排序
    private Integer sortNum;
    //完整名称
    private String wholeName;
    //本区域经度
    private String lon;
    //本区域维度
    private String lat;
    //电话区号
    private String cityCode;
    //邮政编码
    private String zipCode;
    //行政区划代码
    private String areaCode;
    //名称全拼
    private String pinYin;
    //首字母简拼
    private String simplePy;
    //区域名称拼音的第一个字母
    private String perPinYin;
    //省级编码
    private String provinceCode;
    //省级名称
    private String provinceName;
    //城市名称
    private String cityName;
}

