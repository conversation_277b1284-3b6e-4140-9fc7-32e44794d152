package com.sibuqu.common.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 证书信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_certificate_info")
@ApiModel(value = "CertificateInfo对象", description = "证书信息表")
public class CertificateInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("班级ID")
    @TableField("class_id")
    private Integer classId;

    @ApiModelProperty("班级名称")
    @TableField("class_name")
    private String className;

    @ApiModelProperty("课程ID")
    @TableField("course_id")
    private Integer courseId;

    @ApiModelProperty("用户ID")
    @TableField("user_id")
    private Integer userId;

    @ApiModelProperty("用户名称")
    @TableField("user_name")
    private String userName;

    @ApiModelProperty("证书标题")
    @TableField("title")
    private String title;

    @ApiModelProperty("证书编号-规则生成")
    @TableField("cert_num")
    private String certNum;

    @ApiModelProperty("证书类型：0-a系列结业 1-a系列毕业；10-阳明一期普通，11阳明一期优秀，12阳明一期荣誉")
    @TableField("type")
    private Integer type;

    @ApiModelProperty("证书标识icon类型：0-无；1-优秀学员，2-优秀小组，3-荣誉学员，4-优秀学员+优秀小组，5-优秀组长")
    @TableField("icon_type")
    private Integer iconType;

    @ApiModelProperty("注册手机号码")
    @TableField("phone")
    private String phone;

    @ApiModelProperty("学习开始时间")
    @TableField("study_start_time")
    private LocalDateTime studyStartTime;

    @ApiModelProperty("学习结束时间")
    @TableField("study_end_time")
    private LocalDateTime studyEndTime;

    @ApiModelProperty("毕业时间")
    @TableField("graduate_time")
    private Long graduateTime;

    @ApiModelProperty("奖章（0.无 1.有）")
    @TableField("medal_type")
    private Boolean medalType;

    @ApiModelProperty("课程数量")
    @TableField("course_count")
    private Integer courseCount;

    @ApiModelProperty("作业数量")
    @TableField("hwork_count")
    private Integer hworkCount;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除（0.未删除 1.已删除）")
    @TableField("delete_flag")
    @TableLogic
    private Integer deleteFlag;


}
