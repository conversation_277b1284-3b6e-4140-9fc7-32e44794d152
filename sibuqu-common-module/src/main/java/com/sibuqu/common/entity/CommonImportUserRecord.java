package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公共导入用户记录表
 */
@ApiModel(description = "公共导入用户记录表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_import_user_record")
public class CommonImportUserRecord implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    @TableField(value = "data_type")
    @ApiModelProperty(value = "数据类型 1-弹窗 2-广告位 3-版本更新人员")
    private Integer dataType;

    @TableField(value = "record_id")
    @ApiModelProperty(value = "导入记录id")
    private Integer recordId;

    @TableField(value = "data_id")
    @ApiModelProperty(value = "数据id")
    private Integer dataId;

    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户id")
    private String userId;

    @TableField(value = "user_phone")
    @ApiModelProperty(value = "用户手机号")
    private String userPhone;

    @TableField(value = "username")
    @ApiModelProperty(value = "用户姓名")
    private String username;

    @TableField(value = "import_status")
    @ApiModelProperty(value = "导入状态 1-成功 0-失败")
    private Integer importStatus;

    @TableField(value = "fail_code")
    @ApiModelProperty(value = "失败原因编码 1-用户ID不存在 2-用户ID输入错误不符合要求 3-用户ID重复")
    private Integer failCode;

    @TableField(value = "fail_desc")
    @ApiModelProperty(value = "失败原因详情 1-尚未查找到该用户 2-用户ID不符合要求 3-用户ID已存在")
    private String failDesc;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人id")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "操作人id")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = " 更新时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}