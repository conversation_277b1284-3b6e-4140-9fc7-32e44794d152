package com.sibuqu.common.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 公共字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
@Getter
@Setter
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("common_dictionary")
@ApiModel(value = "Dictionary对象", description = "公共字典表")
public class Dictionary implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("字典类型（1课程编码 2企业行业 3热搜关键词 4帮助中心地址 5企业标签 10年龄，11性别，12职位，13企业员工数，14营业额 15人生阶段 16 是否为独生子 17 是否有宝宝 18 婚姻状况 19学历 20在职时长 21团队人数）")
    @TableField("dict_type")
    private Integer dictType;

    @ApiModelProperty("字典值")
    @TableField("dict_value")
    private String dictValue;

    @ApiModelProperty("字典名称")
    @TableField("dict_name")
    private String dictName;
    
    @ApiModelProperty("排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty("删除状态（1是 0否）")
    @TableField("delete_flag")
    @TableLogic
    private Boolean deleteFlag;

    @ApiModelProperty("创建人用户id")
    @TableField("create_user_id")
    private Integer createUserId;

    @ApiModelProperty("创建人姓名")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("更新人用户id")
    @TableField("update_user_id")
    private Integer updateUserId;

    @ApiModelProperty("更新人姓名")
    @TableField("update_user_name")
    private String updateUserName;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

}
