package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 广告位配置
 */
@ApiModel(description = "广告位配置")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ads_config")
public class AdsConfig implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    @TableField(value = "title")
    @ApiModelProperty(value = "标题")
    private String title;

    @TableField(value = "load_status")
    @ApiModelProperty(value = "上下架状态 0-下架 1-上架 ")
    private Integer loadStatus;

    @TableField(value = "show_type")
    @ApiModelProperty(value = "展示位置 1-课程首页banner 2-首页中部广告位 3-全部课程顶部 4-我的广告位 5-个人主页 6-首页专区顶部banner 7-首页底部banner 8-每日金句底部 9-app开屏页 10-新版首页banner 11-新版首页banner20250410 100-四维战略首页顶部 101-四维战略侧边栏 102-我的主场")
    private Integer showType;

    @TableField(value = "show_user_type")
    @ApiModelProperty(value = "展示用户类型：1-全部用户 2-注册用户 3-付费用户 4-课程学员")
    private Integer showUserType;

    @TableField(value = "edition_type")
    @ApiModelProperty(value = "版本类型 1-个人版 2-所有企业 3-单个企业 4-润泽园官方小程序 5-天地常新小程序 100-四维战略")
    private Integer editionType;

    @TableField(value = "company_id")
    @ApiModelProperty(value = "企业 id")
    private Integer companyId;

    @TableField(value = "begin_time")
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime beginTime;

    @TableField(value = "end_time")
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @TableField(value = "file_type")
    @ApiModelProperty(value = "文件类型 1-图片 2-视频")
    private Integer fileType;

    @TableField(value = "img_url")
    @ApiModelProperty(value = "图片地址")
    private String imgUrl;

    @TableField(value = "jump_type")
    @ApiModelProperty(value = "跳转类型 1-不跳转 2-自定义连接 3-商品 4-课件 5-独立直播 6-小程序会议页 7-企业空间 16-请教阳明先生 19-AI收纳箱")
    private Integer jumpType;

    @TableField(value = "jump_url")
    @ApiModelProperty(value = "跳转链接")
    private String jumpUrl;

    @TableField(value = "jump_course_id")
    @ApiModelProperty(value = "课程id")
    private Integer jumpCourseId;

    @TableField(value = "jump_goods_id")
    @ApiModelProperty(value = "商品id")
    private Integer jumpGoodsId;

    @TableField(value = "jump_course_time_table_id")
    @ApiModelProperty(value = "课件id/直播id")
    private Integer jumpCourseTimeTableId;

    @TableField(value = "jump_data_id")
    @ApiModelProperty(value = "跳转 dataId")
    private Long jumpDataId;

    @TableField(value = "order_num")
    @ApiModelProperty(value = "排序序号")
    private Integer orderNum;

    @TableField(value = "only_show")
    @ApiModelProperty(value = "唯一展示 1-是唯一展示 0-不是唯一展示")
    private Integer onlyShow;

    @TableField(value = "visible_range")
    @ApiModelProperty(value = "人员限制 1-所有人 2-按课程权限 3-按企业用户 4-指定用户 5-新注册用户")
    private Integer visibleRange;

    @TableField(value = "visible_range_config")
    @ApiModelProperty(value = "人员限制对应的配置")
    private String visibleRangeConfig;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人 id")
    private Integer createUserId;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人 id")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "tip_copy")
    @ApiModelProperty(value = "提示文案")
    private String tipCopy;

    private static final long serialVersionUID = 1L;
}