package com.sibuqu.common.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 课程或班级分享图
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_course_poster_share")
@ApiModel(value = "CommonCoursePosterShare对象", description = "课程或班级分享图")
public class CoursePosterShare implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("企业ID")
    @TableField("company_id")
    private Integer companyId;

    @ApiModelProperty("课程ID")
    @TableField("course_id")
    private Integer courseId;

    @ApiModelProperty("海报名称")
    @TableField("name")
    private String name;

    @ApiModelProperty("海报分享图地址")
    @TableField("share_poster_url")
    private String sharePosterUrl;

    @ApiModelProperty("海报类型（1.课程海报  2.邀请加班海报 3.战略APP课程海报 4.日收获海报）")
    @TableField("type")
    private Integer type;

    @ApiModelProperty("海报样式图地址")
    @TableField("poster_style_url")
    private String posterStyleUrl;

    @ApiModelProperty("海报样式的第几张 1开始")
    @TableField("poster_style_index")
    private Integer posterStyleIndex;

    @ApiModelProperty("分享导语")
    @TableField("share_words")
    private String shareWords;

    @ApiModelProperty("扫码的导语")
    @TableField("scan_words")
    private String scanWords;

    @ApiModelProperty("海报字体颜色")
    @TableField("font_color")
    private String fontColor;

    @ApiModelProperty("排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty("描述")
    @TableField("desc_info")
    private String descInfo;

    @ApiModelProperty("创建人id")
    @TableField("create_user_id")
    private Integer createUserId;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("操作人id")
    @TableField("update_user_id")
    private Integer updateUserId;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("创建人姓名")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("更新人姓名")
    @TableField("update_user_name")
    private String updateUserName;

    @ApiModelProperty("状态1正常2停用")
    @TableField("data_flag")
    private Integer dataFlag;

    @ApiModelProperty("是否删除0未删除1已删除")
    @TableField("delete_flag")
    private Integer deleteFlag;


}
