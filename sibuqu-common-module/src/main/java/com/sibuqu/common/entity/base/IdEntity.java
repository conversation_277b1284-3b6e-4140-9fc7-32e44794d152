package com.sibuqu.common.entity.base;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: ID
 * @author: haolb
 * @create: 2020-08-05
 **/
@Data
public class IdEntity implements Serializable {

    private static final long serialVersionUID = 6544969427802587666L;

    @ApiModelProperty(value = "主键ID",required = true)
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    public Integer id;

}
