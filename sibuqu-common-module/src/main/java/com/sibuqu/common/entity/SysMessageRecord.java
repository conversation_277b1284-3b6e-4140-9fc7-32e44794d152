package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 消息已读未读记录表
 */
@ApiModel(value = "消息已读未读记录表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_message_record")
public class SysMessageRecord implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 消息id
     */
    @TableField(value = "message_id")
    @ApiModelProperty(value = "消息id")
    private Integer messageId;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户id")
    private Integer userId;

    /**
     * 阅读状态 0未读1已读
     */
    @TableField(value = "read_status")
    @ApiModelProperty(value = "阅读状态 0未读1已读")
    private Integer readStatus;

    /**
     * 删除状态0未删除1已删除
     */
    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态0未删除1已删除")
    private Integer deleteFlag;

    /**
     * 推送消息的类型：1：系统消息 2：活动消息 3：评论和回复消息 4：点赞消息 5：学习提醒
     */
    @TableField(value = "push_type")
    @ApiModelProperty(value = "推送消息的类型：1：系统消息 2：活动消息 3：评论和回复消息 4：点赞消息 5：学习提醒")
    private Integer pushType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人id")
    private Integer createUserId;

    /**
     * 操作人id
     */
    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "操作人id")
    private Integer updateUserId;

    private static final long serialVersionUID = 1L;
}