package com.sibuqu.common.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sibuqu.common.annation.CryptEntity;
import com.sibuqu.common.annation.CryptField;
import com.sibuqu.common.enums.EncryptTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.lang.reflect.Type;
import java.time.LocalDateTime;

/**
 * <p>
 * 评论回复记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_comment_reply_record")
@ApiModel(value = "CommentReplyRecord对象", description = "评论回复记录表")
//@CryptEntity
public class CommentReplyRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("评论ID")
    @TableField("comment_id")
    private Integer commentId;

    @ApiModelProperty("上级回复ID")
    @TableField("parent_id")
    private Integer parentId;

    @ApiModelProperty("上级回复用户ID")
    @TableField("parent_user_id")
    private Integer parentUserId;

    @ApiModelProperty("上级回复用户名称")
    @TableField("parent_user_name")
    private String parentUserName;

    @ApiModelProperty("回复用户ID")
    @TableField("user_id")
    private Integer userId;

    @ApiModelProperty("回复用户名称")
    @TableField("user_name")
    private String userName;

    @ApiModelProperty("回复用户头像")
    @TableField("user_avatar")
    private String userAvatar;

    @ApiModelProperty("回复用户手机号")
    @TableField("user_phone")
//    @CryptField(type = EncryptTypeEnum.AES)
    private String userPhone;

    @TableField(value = "company_id")
    @ApiModelProperty(value = "企业 id")
    private Integer companyId;

    @ApiModelProperty("回复内容")
    @TableField("content")
    private String content;

    @ApiModelProperty("审核状态(0.AI自动判断未通过 1.AI自动判断通过 2.人工审核未通过 3.人工审核通过)")
    @TableField("check_status")
    private Integer checkStatus;

    @ApiModelProperty("审核人ID")
    @TableField("check_user_id")
    private Integer checkUserId;

    @ApiModelProperty("审核人名称")
    @TableField("check_user_name")
    private String checkUserName;

    @ApiModelProperty("回复时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("来源类型（1.后台管理系统 2.APP）")
    @TableField("source_type")
    private Integer sourceType;

    @ApiModelProperty("平台类型（0.企业版 1.个人版）")
    @TableField("platform_type")
    private Integer platformType;

    @ApiModelProperty("显示状态（0.隐藏 1.显示）")
    @TableField("data_flag")
    private Integer dataFlag;

    @TableField(value = "ip_address")
    @ApiModelProperty(value = "ip地址")
    private String ipAddress;

    @TableField(value = "ip_location")
    @ApiModelProperty(value = "ip归属地")
    private String ipLocation;

    @ApiModelProperty("是否删除（0.未删除 1.已删除）")
    @TableField("delete_flag")
    @TableLogic
    private Integer deleteFlag;

}
