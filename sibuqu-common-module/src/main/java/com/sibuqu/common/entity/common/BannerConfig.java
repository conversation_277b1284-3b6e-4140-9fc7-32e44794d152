package com.sibuqu.common.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * banner配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_banner_config")
@ApiModel(value = "BannerConfig对象", description = "banner配置表")
public class BannerConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("企业ID")
    @TableField("company_id")
    private Integer companyId;

    @ApiModelProperty("标题")
    @TableField("title")
    private String title;

    @ApiModelProperty("跳转连接")
    @TableField("url")
    private String url;

    @ApiModelProperty("图片地址")
    @TableField("img_url")
    private String imgUrl;

    @ApiModelProperty("展示开始时间")
    @TableField("show_time_start")
    private LocalDateTime showTimeStart;

    @ApiModelProperty("展示结束时间")
    @TableField("show_time_end")
    private LocalDateTime showTimeEnd;

    @ApiModelProperty("展示用户类型：0-普通用户；1-付费用户；2-课程用户")
    @TableField("show_user_type")
    private Integer showUserType;

    @ApiModelProperty("是否需要跳转 0-需要跳转；1-不需要")
    @TableField("jump_need")
    private Integer jumpNeed;

    @ApiModelProperty("跳转类型：0-自定义连接；1-课程；2-课件；3-直播；4-图文内容")
    @TableField("jump_type")
    private Integer jumpType;

    @ApiModelProperty("展示顺序")
    @TableField("sort_num")
    private Integer sortNum;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("时间显示状态：0-未开始；1-进行中；2-结束；")
    @TableField("data_status")
    private Integer dataStatus;

    @ApiModelProperty("展示状态：1-展示；2-禁用")
    @TableField("data_flag")
    private Integer dataFlag;

    @ApiModelProperty("删除状态（0未删除 1已删除）")
    @TableField("delete_flag")
    @TableLogic
    private Boolean deleteFlag;

    @ApiModelProperty("创建人ID")
    @TableField("create_user_id")
    private Integer createUserId;

    @ApiModelProperty("操作人id")
    @TableField("update_user_id")
    private Integer updateUserId;

}
