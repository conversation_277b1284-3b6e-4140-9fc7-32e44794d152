package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 话题表
 */
@ApiModel(description = "话题表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_topic")
public class CommonTopic implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "title")
    @ApiModelProperty(value = "标题")
    private String title;

    @TableField(value = "topic_desc")
    @ApiModelProperty(value = "描述")
    private String topicDesc;

    @TableField(value = "link_count")
    @ApiModelProperty(value = "关联数量")
    private Integer linkCount;

    @TableField(value = "data_type")
    @ApiModelProperty(value = "关联数据类型 1-课程下班级内容 2-广场 3-专区 4-inno周作业")
    private Integer dataType;

    @TableField(value = "data_id")
    @ApiModelProperty(value = "关联数据id")
    private Long dataId;

    @TableField(value = "recommend_flag")
    @ApiModelProperty(value = "推荐标识 0-不推荐 1-推荐")
    private Integer recommendFlag;

    @TableField(value = "recommend_sort")
    @ApiModelProperty(value = "推荐序号 越小越靠前")
    private Integer recommendSort;

    @TableField(value = "data_flag")
    @ApiModelProperty(value = "上下架状态 0-下架 1-上架")
    private Integer dataFlag;

    @TableField(value = "begin_time")
    @ApiModelProperty(value = "话题开始日期")
    private LocalDateTime beginTime;

    @TableField(value = "end_time")
    @ApiModelProperty(value = "话题结束日期")
    private LocalDateTime endTime;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人ID")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "修改人ID")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    @TableLogic
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}