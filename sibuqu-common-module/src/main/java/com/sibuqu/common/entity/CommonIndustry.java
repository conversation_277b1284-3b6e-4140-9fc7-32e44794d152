package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 所属行业基表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("common_industry")
@ApiModel(value = "CommonIndustry对象", description = "所属行业基表")
public class CommonIndustry implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("行业名称")
    @TableField("name")
    private String name;

    @ApiModelProperty("上级行业ID，int类型，如果没有上级行业，该值为0")
    @TableField("parent_id")
    private Integer parentId;

    @ApiModelProperty("可以根据该字段进行行业的排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty("行业的树形级别")
    @TableField("level")
    private Integer level;

    @ApiModelProperty("状态1正常2停用")
    @TableField("data_flag")
    private Integer dataFlag;


    @ApiModelProperty("创建人id")
    @TableField("create_user_id")
    private Integer createUserId;

    @ApiModelProperty("创建人姓名")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("修改操作时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改操作人id")
    @TableField("update_user_id")
    private Integer updateUserId;

    @ApiModelProperty("修改操作人姓名")
    @TableField("update_user_name")
    private String updateUserName;

    @ApiModelProperty("删除状态（1是 0否）")
    @TableField("delete_flag")
    private Integer deleteFlag;
}
