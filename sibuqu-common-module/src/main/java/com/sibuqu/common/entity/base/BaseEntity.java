package com.sibuqu.common.entity.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @author: haolb
 * @create: 2020-08-05
 **/
@Data
public class BaseEntity extends IdEntity implements Serializable {

    private static final long serialVersionUID = -8104546374773705228L;

    @ApiModelProperty(value = "创建时间",required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    public  Date createTime;

    @ApiModelProperty(value = "更新时间",required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    public Date updateTime;


}
