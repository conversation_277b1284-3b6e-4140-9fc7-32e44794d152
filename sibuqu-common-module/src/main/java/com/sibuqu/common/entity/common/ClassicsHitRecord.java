package com.sibuqu.common.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 经典撞击记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-23
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_classics_hit_record")
@ApiModel(value = "ClassicsHitRecord对象", description = "经典撞击记录")
public class ClassicsHitRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("用户ID")
    @TableField("user_id")
    private Integer userId;

    @ApiModelProperty("用户手机号")
    @TableField("user_phone")
    private String userPhone;

    @ApiModelProperty("撞击课程名称")
    @TableField("hit_course_title")
    private String hitCourseTitle;

    @ApiModelProperty("撞击课程ID")
    @TableField("hit_course_id")
    private Integer hitCourseId;

    @ApiModelProperty("撞击内容")
    @TableField("hit_content")
    private String hitContent;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;


}
