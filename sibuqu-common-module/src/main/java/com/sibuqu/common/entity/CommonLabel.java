package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_label")
@ApiModel(value = "CommonLabel对象", description = "")
public class CommonLabel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("上级标签ID")
    @TableField("parent_id")
    private Integer parentId;

    @ApiModelProperty("名称")
    @TableField("name")
    private String name;

    @ApiModelProperty("标签类型 1-心得标签 2-商品标签 3-内容标签")
    @TableField("label_type")
    private Integer labelType;

    @ApiModelProperty("标签色值")
    @TableField("label_color")
    private String labelColor;

    @ApiModelProperty("跳转类型 1-不跳转 2-跳转链接 3-跳转商品")
    @TableField("jump_type")
    private Integer jumpType;

    @ApiModelProperty("跳转位置")
    @TableField("jump_location")
    private String jumpLocation;

    @ApiModelProperty("描述")
    @TableField("label_desc")
    private String labelDesc;

    @ApiModelProperty("上下架状态 0-下架 1-上架")
    @TableField("data_flag")
    private Integer dataFlag;

    @ApiModelProperty("展示商品id列表")
    @TableField("show_range")
    private String showRange;

    @ApiModelProperty("标签级别 1-一级标签 2-二级标签")
    @TableField("level")
    private Integer level;

    @ApiModelProperty("标签排序 越小越前")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty("创建人ID")
    @TableField("create_user_id")
    private Integer createUserId;

    @ApiModelProperty("创建人名称")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("修改人ID")
    @TableField("update_user_id")
    private Integer updateUserId;

    @ApiModelProperty("修改人名称")
    @TableField("update_user_name")
    private String updateUserName;

    @ApiModelProperty("修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("删除状态 1-是 0-否")
    @TableField("delete_flag")
    @TableLogic
    private Boolean deleteFlag;


}
