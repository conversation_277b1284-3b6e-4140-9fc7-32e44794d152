package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 单个页面设置
 */
@ApiModel(description = "单个页面设置")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "page_single")
public class PageSingle implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    @TableField(value = "title")
    @ApiModelProperty(value = "标题")
    private String title;

    @TableField(value = "page_type")
    @ApiModelProperty(value = "页面类型 1首页 2栏目页面 3内容页面 4独立页面")
    private Integer pageType;

    @TableField(value = "page_group_id")
    @ApiModelProperty(value = "页面组 id")
    private Integer pageGroupId;

    @TableField(value = "column_id")
    @ApiModelProperty(value = "栏目 id")
    private Integer columnId;

    @TableField(value = "column_url")
    @ApiModelProperty(value = "栏目 url")
    private String columnUrl;

    @TableField(value = "order_num")
    @ApiModelProperty(value = "排序序号")
    private Integer orderNum;

    @TableField(value = "recommend_flag")
    @ApiModelProperty(value = "是否推荐 0 不推荐 1 推荐")
    private Integer recommendFlag;

    @TableField(value = "recommend_num")
    @ApiModelProperty(value = "推荐序号")
    private Integer recommendNum;

    @TableField(value = "content")
    @ApiModelProperty(value = "页面内容")
    private String content;

    @TableField(value = "jump_type")
    @ApiModelProperty(value = "跳转类型 1-页面类型 2-地址 3-课程 4-独立直播 5-图片 6-支付页面")
    private Integer jumpType;

    @TableField(value = "jump_content")
    @ApiModelProperty(value = "跳转数据信息")
    private String jumpContent;

    @TableField(value = "jump_content_name")
    @ApiModelProperty(value = "跳转数据信息文字展示")
    private String jumpContentName;

    @TableField(value = "up_status")
    @ApiModelProperty(value = "上下架状态 1-上架 0-下架")
    private Integer upStatus;

    @TableField(value = "annex")
    @ApiModelProperty(value = "附件信息")
    private String annex;

    @TableField(value = "edition_type")
    @ApiModelProperty(value = "版本类型 1-个人版 2-所有企业 3-企业版 4-润泽园官方小程序 5-天地常新小程序")
    private Integer editionType;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人 id")
    private Integer createUserId;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人 id")
    private Integer updateUserId;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}