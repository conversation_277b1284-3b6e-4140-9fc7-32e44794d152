package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户内容扩展表
 */
@ApiModel(description = "用户内容扩展表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_content_extend")
public class CommonContentExtend implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "content_id")
    @ApiModelProperty(value = "内容id")
    private Long contentId;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人ID")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "修改人ID")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}