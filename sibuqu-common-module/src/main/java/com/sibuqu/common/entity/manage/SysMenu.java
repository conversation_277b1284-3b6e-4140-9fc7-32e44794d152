package com.sibuqu.common.entity.manage;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统资源-菜单信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("sys_menu")
@ApiModel(value = "SysMenu对象", description = "系统资源-菜单信息")
public class SysMenu implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("菜单Id")
    @TableId(value = "menu_id", type = IdType.AUTO)
    private Integer menuId;

    @ApiModelProperty("父级菜单")
    @TableField("parent_id")
    private Integer parentId;

    @ApiModelProperty("菜单编码")
    @TableField("menu_code")
    private String menuCode;

    @ApiModelProperty("菜单名称")
    @TableField("menu_name")
    private String menuName;

    @ApiModelProperty("描述")
    @TableField("menu_desc")
    private String menuDesc;

    @ApiModelProperty("路径前缀")
    @TableField("scheme")
    private String scheme;

    @ApiModelProperty("请求路径")
    @TableField("path")
    private String path;

    @ApiModelProperty("菜单标题")
    @TableField("icon")
    private String icon;

    @ApiModelProperty("打开方式:_self窗口内,_blank新窗口")
    @TableField("target")
    private String target;

    @ApiModelProperty("优先级 越小越靠前")
    @TableField("priority")
    private Integer priority;

    @ApiModelProperty("状态:0-无效 1-有效")
    @TableField("status")
    private Integer status;

    @ApiModelProperty("保留数据0-否 1-是 不允许删除")
    @TableField("is_persist")
    private Integer isPersist;

    @ApiModelProperty("服务名")
    @TableField("service_id")
    private String serviceId;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人_用户id")
    @TableField("create_user_id")
    private Integer createUserId;

    @ApiModelProperty("创建人名称")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人_用户id")
    @TableField("update_user_id")
    private Integer updateUserId;

    @ApiModelProperty("修改人")
    @TableField("update_user_name")
    private String updateUserName;


}
