package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 文件导出记录
    */
@ApiModel(description="文件导出记录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_export_file")
public class CommonExportFile implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 关联数据类型 1-学习会直播点报名数据 2-学习会直播点数据
     */
    @TableField(value = "data_type")
    @ApiModelProperty(value="关联数据类型 1-学习会直播点报名数据 2-学习会直播点数据")
    private Integer dataType;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    @ApiModelProperty(value="文件名称")
    private String fileName;

    /**
     * 文件下载地址
     */
    @TableField(value = "file_url")
    @ApiModelProperty(value="文件下载地址")
    private String fileUrl;

    /**
     * 导出状态 1-未开始 2-执行中 3-执行成功 4-执行失败
     */
    @TableField(value = "export_status")
    @ApiModelProperty(value="导出状态 1-未开始 2-执行中 3-执行成功 4-执行失败")
    private Integer exportStatus;

    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty(value="创建人ID")
    private Integer createUserId;

    /**
     * 创建人名称
     */
    @TableField(value = "create_user_name")
    @ApiModelProperty(value="创建人名称")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(value = "update_user_id")
    @ApiModelProperty(value="修改人ID")
    private Integer updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name")
    @ApiModelProperty(value="修改人名称")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="修改时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}