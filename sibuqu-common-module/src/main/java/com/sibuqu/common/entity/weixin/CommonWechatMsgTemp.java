package com.sibuqu.common.entity.weixin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 微信下的消息模板消息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("common_wechat_msg_temp")
@ApiModel(value = "CommonWechatMsgTemp对象", description = "微信下的消息模板消息")
public class CommonWechatMsgTemp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("标题")
    @TableField("title")
    private String title;

    @ApiModelProperty("服务号的模板id")
    @TableField("service_number_template_id")
    private String serviceNumberTemplateId;

    @ApiModelProperty("模板类型 1:微信服务号")
    @TableField("type")
    private Integer type;

    @ApiModelProperty("发送的模板消息data值json格式")
    @TableField("template_data_json")
    private String templateDataJson;

    @ApiModelProperty(value = "发送对象 1:数据标签 2:课程")
    @TableField("send_object")
    private Integer sendObject;

    @ApiModelProperty("推送时间")
    @TableField("send_date")
    private LocalDateTime sendDate;

    @ApiModelProperty("发布状态: 0:未开启 1:已完成 2：已关闭")
    @TableField("publish_status")
    private Integer publishStatus;

    @ApiModelProperty("发送类型  0:手动 1:自动")
    @TableField("send_type")
    private Integer sendType;

    @ApiModelProperty("模板跳转类型  0:不跳转 1:H5页面链接 2:小程序链接")
    @TableField("temp_jump_type")
    private Integer tempJumpType;

    @ApiModelProperty("链接地址")
    @TableField("link_address")
    private String linkAddress;

    @ApiModelProperty("模板小程序下的appid")
    @TableField("mini_app_id")
    private String miniAppId;

    @ApiModelProperty("具体发送模板的json信息")
    @TableField("message_template_json")
    private String messageTemplateJson;

    @ApiModelProperty("删除状态（1是 0否）")
    @TableField("delete_flag")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人_用户id")
    @TableField("create_user_id")
    private Integer createUserId;

    @ApiModelProperty("创建人名称")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人_用户id")
    @TableField("update_user_id")
    private Integer updateUserId;

    @ApiModelProperty("修改人")
    @TableField("update_user_name")
    private String updateUserName;

    @ApiModelProperty("备注信息")
    @TableField("remarks")
    private String remarks;


    @ApiModelProperty("服务号模板的内容")
    @TableField("template_content")
    private String templateContent;

    @ApiModelProperty("服务号模板的内容案例")
    @TableField("template_example")
    private String templateExample;

    @ApiModelProperty("服务号模板的标题")
    @TableField("template_title")
    private String templateTitle;





}
