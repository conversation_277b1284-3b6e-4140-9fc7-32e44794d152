package com.sibuqu.common.entity.manage;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统权限-角色关联
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("sys_menu_role")
@ApiModel(value = "SysMenuRole对象", description = "系统权限-角色关联")
public class SysMenuRole implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("菜单ID")
    @TableField("menu_id")
    private Integer menuId;

    @ApiModelProperty("角色ID")
    @TableField("role_id")
    private Integer roleId;

    @ApiModelProperty("过期时间:null表示长期")
    @TableField("expire_time")
    private LocalDateTime expireTime;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;


}
