package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公共弹窗
 */
@ApiModel(description = "公共弹窗")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "app_dialog_box")
public class AppDialogBox implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 活动标题
     */
    @TableField(value = "title")
    @ApiModelProperty(value = "活动标题")
    private String title;

    /**
     * 弹窗位置类型 1:APP首页 2:商品详情页 3:课程播放页 4:独立直播 5:学习页 6:个人中心页 7:班级主页 8:小程序首页
     */
    @TableField(value = "position_type")
    @ApiModelProperty(value = "弹窗位置类型 1:APP首页 2:商品详情页 3:课程播放页 4:独立直播 5:学习页 6:个人中心页 7:班级主页 8:小程序首页")
    private Integer positionType;

    /**
     * 跳转类型 1-内部链接 2-商品页 3-课件页 4-不跳转 5-独立直播
     */
    @TableField(value = "jump_type")
    @ApiModelProperty(value = "跳转类型 1-内部链接 2-商品页 3-课件页 4-不跳转 5-独立直播")
    private Integer jumpType;

    /**
     * 推送人员类型 1:全部用户 2:按照课程权限 3:指定用户 11:四维战略全部人员
     */
    @TableField(value = "push_person_type")
    @ApiModelProperty(value = "推送人员类型 1:全部用户 2:按照课程权限 3:指定用户 11:四维战略全部人员")
    private Integer pushPersonType;

    /**
     * 开始时间
     */
    @TableField(value = "begin_time")
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 背景图片
     */
    @TableField(value = "bg_url")
    @ApiModelProperty(value = "背景图片")
    private String bgUrl;

    /**
     * 平台类型 0-企业版 1-个人版 2-小程序
     */
    @TableField(value = "platform_type")
    @ApiModelProperty(value = "平台类型 0-企业版 1-个人版 2-小程序 100-四维战略")
    private Integer platformType;

    /**
     * 弹窗次数
     */
    @TableField(value = "`count`")
    @ApiModelProperty(value = "弹窗次数")
    private Integer count;

    /**
     * 发送人数
     */
    @TableField(value = "send_user_total")
    @ApiModelProperty(value = "发送人数")
    private Integer sendUserTotal;

    /**
     * 上下架状态 1上架 0 下架
     */
    @TableField(value = "load_status")
    @ApiModelProperty(value = "上下架状态 1上架 0 下架")
    private Integer loadStatus;

    /**
     * 商品 id 列表,用逗号分割
     */
    @TableField(value = "goods_id_list")
    @ApiModelProperty(value = "商品 id 列表,用逗号分割")
    private String goodsIdList;

    /**
     * 商品名称列表,用逗号分割
     */
    @TableField(value = "goods_name_list")
    @ApiModelProperty(value = "商品名称列表,用逗号分割")
    private String goodsNameList;

    /**
     * 课程 id 列表,用逗号分割
     */
    @TableField(value = "course_id_list")
    @ApiModelProperty(value = "课程 id 列表,用逗号分割")
    private String courseIdList;

    /**
     * 课程名称列表,用逗号分割
     */
    @TableField(value = "course_name_list")
    @ApiModelProperty(value = "课程名称列表,用逗号分割")
    private String courseNameList;

    /**
     * 直播 id 列表,用逗号分割
     */
    @TableField(value = "live_id_list")
    @ApiModelProperty(value = "直播 id 列表,用逗号分割")
    private String liveIdList;

    /**
     * 直播名称列表,用逗号分割
     */
    @TableField(value = "live_name_list")
    @ApiModelProperty(value = "直播名称列表,用逗号分割")
    private String liveNameList;

    /**
     * 导入文件地址
     */
    @TableField(value = "import_file_url")
    @ApiModelProperty(value = "导入文件地址")
    private String importFileUrl;

    /**
     * 预览用 0 非预览 1 预览
     */
    @TableField(value = "preview_flag")
    @ApiModelProperty(value = "预览用 0 非预览 1 预览")
    private Integer previewFlag;

    /**
     * 跳转内部链接
     */
    @TableField(value = "jump_url")
    @ApiModelProperty(value = "跳转内部链接")
    private String jumpUrl;

    /**
     * 商品id
     */
    @TableField(value = "jump_goods_id")
    @ApiModelProperty(value = "商品id")
    private Integer jumpGoodsId;

    /**
     * 商品名称
     */
    @TableField(value = "jump_goods_name")
    @ApiModelProperty(value = "商品名称")
    private String jumpGoodsName;

    /**
     * 课件id
     */
    @TableField(value = "jump_rel_id")
    @ApiModelProperty(value = "课件id")
    private Integer jumpRelId;

    /**
     * 课件名称
     */
    @TableField(value = "jump_rel_name")
    @ApiModelProperty(value = "课件名称")
    private String jumpRelName;

    /**
     * 推送用户批次 id（Excel 使用）
     */
    @TableField(value = "push_person_batch_id")
    @ApiModelProperty(value = "推送用户批次 id（Excel 使用）")
    private String pushPersonBatchId;

    /**
     * 推送用户课程权限,用逗号分割
     */
    @TableField(value = "push_person_source_info")
    @ApiModelProperty(value = "推送用户课程权限,用逗号分割")
    private String pushPersonSourceInfo;

    /**
     * 推送用户课程权限名称列表,用逗号分割
     */
    @TableField(value = "push_person_source_name")
    @ApiModelProperty(value = "推送用户课程权限名称列表,用逗号分割")
    private String pushPersonSourceName;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人id")
    private Integer createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人id")
    private Integer updateUserId;

    /**
     * 更新人全名
     */
    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人全名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = " 更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "app_source")
    @ApiModelProperty(value = "app来源 1:润泽园app 2:战略创新app")
    private Integer appSource;

    private static final long serialVersionUID = 1L;
}