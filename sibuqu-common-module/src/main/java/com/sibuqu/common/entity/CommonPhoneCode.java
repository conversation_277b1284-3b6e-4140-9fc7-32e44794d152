package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sibuqu.common.entity.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 手机验证码
 * @author: yangyy
 * @create: 2021-12-26
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("common_phone_code")
public class CommonPhoneCode extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 8687309175566354045L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String phone;

    private String code;

    private Date createTime;
}
