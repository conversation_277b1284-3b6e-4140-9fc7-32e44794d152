package com.sibuqu.common.entity.manage;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统角色-用户关联
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("sys_role_user")
@ApiModel(value = "SysRoleUser对象", description = "系统角色-用户关联")
public class SysRoleUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", dataType = "Integer")
    @TableField("user_id")
    private Integer userId;

    @ApiModelProperty(value = "角色ID", dataType = "Integer")
    @TableField("role_id")
    private Integer roleId;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;


}
