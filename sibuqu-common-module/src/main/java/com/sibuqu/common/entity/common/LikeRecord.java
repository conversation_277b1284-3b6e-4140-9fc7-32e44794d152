package com.sibuqu.common.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 点赞记录表
 */
@ApiModel(description = "点赞记录表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_like_record")
public class LikeRecord implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    @TableField(value = "data_id")
    @ApiModelProperty(value = "关联数据ID")
    private Integer dataId;

    @TableField(value = "company_id")
    @ApiModelProperty(value = "企业 id")
    private Integer companyId;

    @TableField(value = "like_type")
    @ApiModelProperty(value = "点赞类型 1-心得点赞 2-课件评论点赞 3-课件资源点赞 4-成长专区点赞 5-小程序心得点赞 6-小程序视频点赞 7-小程序会议点赞 11-小组文字点赞 12-小组视频点赞 13-小程序会议问题 14-家庭幸福暑期活动 15-小组共读 16-家书 17-成长案例 18-知行卡践行 19-小程序打卡 20-共读任务 21-共读任务记录 22-想法 23-十家连心周报 24-润泽头条音频点赞 25-橐龠会议内容 26-企业主页 27-用户学习排行榜 28-成长计划心得 29-课程笔记 30-公共内容 31-信箱 32-公共小视频 33-评论 34-回复")
    private Integer likeType;

    @TableField(value = "like_status")
    @ApiModelProperty(value = "点赞状态（0.取消点赞 1.点赞）")
    private Integer likeStatus;

    @TableField(value = "like_count")
    @ApiModelProperty(value = "点赞数量")
    private Integer likeCount;

    @TableField(value = "user_id")
    @ApiModelProperty(value = "点赞用户ID")
    private Integer userId;

    @TableField(value = "user_name")
    @ApiModelProperty(value = "点赞用户名称")
    private String userName;

    @TableField(value = "area_code")
    @ApiModelProperty(value = "省编码")
    private String areaCode;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "点赞时间")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @TableField(value = "platform_type")
    @ApiModelProperty(value = "平台类型（0.企业版 1.个人版）")
    private Integer platformType;

    private static final long serialVersionUID = 1L;
}