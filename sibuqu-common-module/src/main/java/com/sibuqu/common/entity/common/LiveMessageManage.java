package com.sibuqu.common.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 直播消息管理-图片之类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-26
 */
@Data
@Accessors(chain = true)
@TableName("common_live_message_manage")
@ApiModel(value = "CommonLiveMessageManage对象", description = "直播消息管理-图片之类")
public class LiveMessageManage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("课程id")
    @TableField("course_id")
    private Integer courseId;

    @ApiModelProperty("关联课程时间表ID")
    @TableField("course_timetable_id")
    private Integer courseTimetableId;

    @ApiModelProperty("消息类型，0：文本 1.图片 3.图文消息 4.公告消息 ")
    @TableField("reply_type")
    private Integer replyType;

    @ApiModelProperty("直播回复的内容")
    @TableField("post_content")
    private String postContent;

    @ApiModelProperty("是否已发送 0：否 1：是")
    @TableField("is_send")
    private Integer isSend;

    @ApiModelProperty("用户id")
    @TableField("user_id")
    private Integer userId;

    @ApiModelProperty("消息类型是 图片消息，图片的地址")
    @TableField("image_url")
    private String imageUrl;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人名称")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人_用户id")
    @TableField("update_user_id")
    private Integer updateUserId;

    @ApiModelProperty("修改人名称")
    @TableField("update_user_name")
    private String updateUserName;

    @ApiModelProperty("是否置顶 0:否 1:是")
    @TableField("is_top")
    private Integer isTop;

    @ApiModelProperty("跳转类型： 1 不跳转；2商品；3 h5链接；4 图片,5课件")
    @TableField("goto_type")
    private Integer gotoType;

    @ApiModelProperty("跳转类型- 对应的值")
    @TableField("goto_val")
    private String gotoVal;

    @ApiModelProperty("是置顶,多少秒 默认3秒")
    @TableField("top_second")
    private Integer topSecond;

    @ApiModelProperty("0-有效；1-无效 删除")
    @TableField("delete_flag")
    private Integer deleteFlag;

    @ApiModelProperty("IM消息序列号，唯一标示一条消息")
    @TableField("msg_seq")
    private String msgSeq;
}
