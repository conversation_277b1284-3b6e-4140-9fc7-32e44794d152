package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
    * 页面组默认模板配置
    */
@ApiModel(value="页面组默认模板配置")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "page_template_config")
public class PageTemplateConfig implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    private Integer id;

    /**
     * 页面类型 1首页 2栏目页面 3内容页面 4独立页面
     */
    @TableField(value = "page_type")
    @ApiModelProperty(value="页面类型 1首页 2栏目页面 3内容页面 4独立页面")
    private Integer pageType;

    /**
     * 标题
     */
    @TableField(value = "title")
    @ApiModelProperty(value="标题")
    private String title;

    /**
     * 创建人 id
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty(value="创建人 id")
    private Integer createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人 id
     */
    @TableField(value = "update_user_id")
    @ApiModelProperty(value="更新人 id")
    private Integer updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}