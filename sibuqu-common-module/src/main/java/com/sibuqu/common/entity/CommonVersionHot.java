package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@ApiModel(description = "版本热更新")
@Data
@TableName(value = "common_version_hot")
public class CommonVersionHot implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "device_type")
    @ApiModelProperty(value = "设备类型 PC_HOT IM")
    private String deviceType;

    @TableField(value = "version_code")
    @ApiModelProperty(value = "版本号 1024 值是PC_HOT的值，其他类型同理")
    private Integer versionCode;

    @TableField(value = "url")
    @ApiModelProperty(value = "下载链接")
    private String url;

    @TableField(value = "show_range_min")
    @ApiModelProperty(value = "最小版本可见范围 值是PC_W、PC_M的值，其他类型同理")
    private Integer showRangeMin;

    @TableField(value = "show_range_max")
    @ApiModelProperty(value = "最大版本可见范围 值是PC_W、PC_M的值，其他类型同理")
    private Integer showRangeMax;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人 id")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人 id")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}