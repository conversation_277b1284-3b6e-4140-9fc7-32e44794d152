package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 推送消息开关控制表
 */
@ApiModel(description = "推送消息开关控制表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_push_message_switch")
public class SysPushMessageSwitch implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 用户 id
     */
    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户 id")
    private Integer userId;

    /**
     * 评论和回复消息开关
     */
    @TableField(value = "comment_reply_switch")
    @ApiModelProperty(value = "评论和回复消息开关")
    private Integer commentReplySwitch;

    /**
     * 点赞消息总开关
     */
    @TableField(value = "like_switch")
    @ApiModelProperty(value = "点赞消息总开关")
    private Integer likeSwitch;

    /**
     * 学习提醒消息总开关
     */
    @TableField(value = "study_remind_switch")
    @ApiModelProperty(value = "学习提醒消息总开关")
    private Integer studyRemindSwitch;

    /**
     * 会议提醒开关
     */
    @TableField(value = "meeting_remind_switch")
    @ApiModelProperty(value = "会议提醒开关")
    private Integer meetingRemindSwitch;

    /**
     * 订单消息开关
     */
    @TableField(value = "order_switch")
    @ApiModelProperty(value = "订单消息开关")
    private Integer orderSwitch;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}