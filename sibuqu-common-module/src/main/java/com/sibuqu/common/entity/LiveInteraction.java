package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 直播互动配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_live_interaction")
@ApiModel(value = "LiveInteraction对象", description = "直播互动配置表")
public class LiveInteraction implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键自增")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("用户字体")
    @TableField("user_font")
    private String userFont;

    @ApiModelProperty("用户字体大小")
    @TableField("user_font_size")
    private String userFontSize;

    @ApiModelProperty("评论字体")
    @TableField("comment_font")
    private String commentFont;

    @ApiModelProperty("评论字体大小")
    @TableField("comment_font_size")
    private String commentFontSize;

    @ApiModelProperty("评论行间距")
    @TableField("comment_line_height")
    private String commentLineHeight;

    @ApiModelProperty("消息类型")
    @TableField("msg_type")
    private Integer msgType;

    @ApiModelProperty("直播大屏状态 0开始；1暂停")
    @TableField("live_status")
    private Integer liveStatus;

    @ApiModelProperty("状态 0 有效 1 无效")
    @TableField("status")
    private Integer status;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;


}
