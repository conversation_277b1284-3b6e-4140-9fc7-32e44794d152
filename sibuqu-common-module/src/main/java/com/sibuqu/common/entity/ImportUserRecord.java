package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 导入用户记录表
 */
@ApiModel(value = "导入用户记录表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "import_user_record")
public class ImportUserRecord implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 导入记录id
     */
    @TableField(value = "record_id")
    @ApiModelProperty(value = "导入记录id")
    private Integer recordId;

    /**
     * 消息id
     */
    @TableField(value = "message_id")
    @ApiModelProperty(value = "消息id")
    private Integer messageId;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户id")
    private String userId;

    @TableField(value = "user_phone")
    @ApiModelProperty(value = "手机号")
    private String userPhone;

    /**
     * 用户姓名
     */
    @TableField(value = "username")
    @ApiModelProperty(value = "用户姓名")
    private String username;

    /**
     * 导入状态 1-成功 0-失败
     */
    @TableField(value = "import_status")
    @ApiModelProperty(value = "导入状态 1-成功 0-失败")
    private Integer importStatus;

    /**
     * 失败原因编码
     */
    @TableField(value = "fail_code")
    @ApiModelProperty(value = "失败原因编码")
    private Integer failCode;

    /**
     * 失败原因详情
     */
    @TableField(value = "fail_desc")
    @ApiModelProperty(value = "失败原因详情")
    private String failDesc;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人id")
    private Integer createUserId;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 操作人id
     */
    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "操作人id")
    private Integer updateUserId;

    /**
     * 更新人姓名
     */
    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = " 更新时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}