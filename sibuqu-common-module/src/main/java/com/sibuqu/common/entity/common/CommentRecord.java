package com.sibuqu.common.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 评论记录表
 */
@ApiModel(description = "评论记录表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_comment_record")
@Accessors(chain = true)
public class CommentRecord implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    @TableField(value = "comment_type")
    @ApiModelProperty(value = "评论类型 1-心得评论 2-课件评论 3-成长专区评论 4-小程序心得评论 5-小程序视频评论 6-小程序会议评论 11-小组文字评论 12-小组视频评论 13-小程序会议问题 14-家庭幸福暑期活动 15-小组共读 16-家书 17-成长案例 19-小程序打卡 20-共读任务 21-共读任务记录 22-想法 23-十家连心周报 25-橐龠会议内容 28-成长计划心得 29-课程笔记 30-公共内容 31-信箱")
    private Integer commentType;

    @TableField(value = "data_id")
    @ApiModelProperty(value = "关联数据ID")
    private Integer dataId;

    @TableField(value = "classes_id")
    @ApiModelProperty(value = "班级ID")
    private Integer classesId;

    @TableField(value = "company_id")
    @ApiModelProperty(value = "企业 id")
    private Integer companyId;

    @TableField(value = "user_id")
    @ApiModelProperty(value = "评论用户ID")
    private Integer userId;

    @TableField(value = "user_name")
    @ApiModelProperty(value = "评论用户名称")
    private String userName;

    @TableField(value = "user_avatar")
    @ApiModelProperty(value = "评论用户头像")
    private String userAvatar;

    @TableField(value = "user_phone")
    @ApiModelProperty(value = "评论用户手机号")
    private String userPhone;

    @TableField(value = "content")
    @ApiModelProperty(value = "评论内容")
    private String content;

    @TableField(value = "content_json")
    @ApiModelProperty(value = "评论内容json")
    private String contentJson;

    @TableField(value = "check_status")
    @ApiModelProperty(value = "审核状态(0.AI自动判断未通过 1.AI自动判断通过 2.人工审核未通过 3.人工审核通过)")
    private Integer checkStatus;

    @TableField(value = "check_user_id")
    @ApiModelProperty(value = "审核人ID")
    private Integer checkUserId;

    @TableField(value = "check_user_name")
    @ApiModelProperty(value = "审核人名称")
    private String checkUserName;

    @TableField(value = "top_status")
    @ApiModelProperty(value = "置顶状态（0.未置顶 1.已置顶）")
    private Integer topStatus;

    @TableField(value = "top_time")
    @ApiModelProperty(value = "置顶时间")
    private LocalDateTime topTime;

    @TableField(value = "area_code")
    @ApiModelProperty(value = "省编码")
    private String areaCode;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "评论时间")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "source_type")
    @ApiModelProperty(value = "来源类型（1.PC 2.APP）")
    private Integer sourceType;

    @TableField(value = "data_flag")
    @ApiModelProperty(value = "显示状态（0.隐藏 1.显示）")
    private Integer dataFlag;

    @TableField(value = "platform_type")
    @ApiModelProperty(value = "平台类型（0.企业版 1.个人版）")
    private Integer platformType;

    @TableField(value = "origin_content_id")
    @ApiModelProperty(value = "原始内容id")
    private Long originContentId;

    @TableField(value = "ip_address")
    @ApiModelProperty(value = "ip地址")
    private String ipAddress;

    @TableField(value = "ip_location")
    @ApiModelProperty(value = "ip归属地")
    private String ipLocation;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "是否删除（0.未删除 1.已删除）")
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}