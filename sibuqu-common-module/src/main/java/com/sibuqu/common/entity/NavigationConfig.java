package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导航配置
 */
@ApiModel(description = "导航配置")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "navigation_config")
public class NavigationConfig implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    @TableField(value = "title")
    @ApiModelProperty(value = "标题")
    private String title;

    @TableField(value = "img_url")
    @ApiModelProperty(value = "图片地址")
    private String imgUrl;

    @TableField(value = "select_url")
    @ApiModelProperty(value = "选中图标地址")
    private String selectUrl;

    @TableField(value = "not_select_url")
    @ApiModelProperty(value = "未选中图标地址")
    private String notSelectUrl;

    @TableField(value = "jump_type")
    @ApiModelProperty(value = "跳转类型 1-商品(顶部菜单不用,金刚区用) 2-课程栏目 3-全部课程页 4-H5地址 7-企业空间 8-内部小程序 9-第三方小程序 16-请教阳明先生" +
            " 17-金句 18-头条 19-读书 20-成长计划 21-经典撞击")
    private Integer jumpType;

    @TableField(value = "jump_course_id")
    @ApiModelProperty(value = "课程id")
    private Integer jumpCourseId;

    @TableField(value = "jump_goods_id")
    @ApiModelProperty(value = "商品id")
    private Integer jumpGoodsId;

    @TableField(value = "jump_column_id")
    @ApiModelProperty(value = "栏目id")
    private Integer jumpColumnId;

    @TableField(value = "jump_url")
    @ApiModelProperty(value = "跳转链接")
    private String jumpUrl;

    @TableField(value = "jump_mini_id")
    @ApiModelProperty(value = "小程序id")
    private String jumpMiniId;

    @TableField(value = "order_num")
    @ApiModelProperty(value = "排序序号")
    private Integer orderNum;

    @TableField(value = "load_status")
    @ApiModelProperty(value = "上下架状态 0-下架 1-上架 ")
    private Integer loadStatus;

    @TableField(value = "show_type")
    @ApiModelProperty(value = "展示位置 1-顶部菜单 2-老版金刚区 3-新版金刚区")
    private Integer showType;

    @TableField(value = "edition_type")
    @ApiModelProperty(value = "版本类型 1-个人版 2-所有企业 3-单个企业 4-润泽园官方小程序 5-天地常新小程序")
    private Integer editionType;

    @ApiModelProperty("可见人群限制（1限制 0不限制）")
    @TableField(value = "show_limit_flag")
    private Integer showLimitFlag;

    @ApiModelProperty("可见人群角色限制逗号分隔（1班主任 2组长 3普通学员）")
    @TableField(value = "show_limit_role")
    private String showLimitRole;

    @ApiModelProperty("可见人群课程限制逗号分隔")
    @TableField(value = "show_limit_course")
    private String showLimitCourse;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人 id")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人 id")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}