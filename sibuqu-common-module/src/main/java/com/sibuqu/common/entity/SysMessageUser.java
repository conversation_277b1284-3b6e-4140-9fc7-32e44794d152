package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发送消息导入用户信息表
 */
@ApiModel(value = "发送消息导入用户信息表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_message_user")
public class SysMessageUser implements Serializable {
    /**
     * 主键信息
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键信息")
    private Integer id;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户id")
    private Integer userId;

    /**
     * 批次号
     */
    @TableField(value = "batch_id")
    @ApiModelProperty(value = "批次号")
    private String batchId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建用户id
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建用户id")
    private Integer createUserId;

    private static final long serialVersionUID = 1L;
}