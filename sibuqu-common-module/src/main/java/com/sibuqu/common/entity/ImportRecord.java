package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 导入记录表
 */
@ApiModel(value = "导入记录表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "import_record")
public class ImportRecord implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 文件链接地址
     */
    @TableField(value = "url")
    @ApiModelProperty(value = "文件链接地址")
    private String url;

    /**
     * 导入数量
     */
    @TableField(value = "import_count")
    @ApiModelProperty(value = "导入数量")
    private Integer importCount;

    /**
     * 成功数量
     */
    @TableField(value = "success_count")
    @ApiModelProperty(value = "成功数量")
    private Integer successCount;

    /**
     * 失败数量
     */
    @TableField(value = "fail_count")
    @ApiModelProperty(value = "失败数量")
    private Integer failCount;

    /**
     * 数据id(消息id)
     */
    @TableField(value = "data_id")
    @ApiModelProperty(value = "数据id(消息id)")
    private Integer dataId;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人id")
    private Integer createUserId;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 操作人id
     */
    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "操作人id")
    private Integer updateUserId;

    /**
     * 更新人姓名
     */
    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = " 更新时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}