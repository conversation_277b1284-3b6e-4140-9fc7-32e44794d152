package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 腾讯会议房间信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_room_info")
@ApiModel(value = "RoomInfo对象", description = "腾讯会议房间信息表")
public class RoomInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("房间名称")
    @TableField("room_name")
    private String roomName;

    @ApiModelProperty("腾讯会议的会议号")
    @TableField("tm_room_id")
    private Long tmRoomId;

    @ApiModelProperty("腾讯会议的URL")
    @TableField("tm_room_url")
    private String tmRoomUrl;

    @TableField("room_sort")
    private Long roomSort;

    @ApiModelProperty("当前人数")
    @TableField("current_member")
    private Integer currentMember;

    @ApiModelProperty("会议开始时间")
    @TableField("start_time")
    private LocalDateTime startTime;

    @ApiModelProperty("会议结束时间")
    @TableField("end_time")
    private LocalDateTime endTime;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("状态： 0显示 1隐藏 ")
    @TableField("status")
    private Integer status;

    @ApiModelProperty("课程ID集合，以逗号分隔")
    @TableField("course_ids")
    private String courseIds;


}
