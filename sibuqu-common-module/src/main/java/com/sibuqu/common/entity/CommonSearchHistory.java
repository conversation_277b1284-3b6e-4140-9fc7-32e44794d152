package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 搜索历史记录
 */
@ApiModel(description = "搜索历史记录")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "common_search_history")
public class CommonSearchHistory implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "data_type")
    @ApiModelProperty(value = "关联数据类型 2-广场")
    private Integer dataType;

    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户 id")
    private Integer userId;

    @TableField(value = "content")
    @ApiModelProperty(value = "搜索记录")
    private String content;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人 id")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人 id")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    @TableLogic
    private Integer deleteFlag;
}