package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户反馈
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_user_feedback")
@ApiModel(value = "UserFeedback对象", description = "用户反馈")
public class UserFeedback implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("意见内容")
    @TableField("view_content")
    private String viewContent;

    @ApiModelProperty("用户id")
    @TableField("user_id")
    private Integer userId;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("0-有效；1-无效")
    @TableField("delete_tag")
    private Integer deleteTag;

    @ApiModelProperty("操作人id")
    @TableField("update_user_id")
    private String updateUserId;

    @ApiModelProperty("更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("消息来源(0.app,1.pc)")
    @TableField("source_type")
    private Integer sourceType;

    @ApiModelProperty("老师回复的内容")
    @TableField("reply_user_name")
    private String replyUserName;

    @ApiModelProperty("老师回复的内容")
    @TableField("reply_content")
    private String replyContent;

    @ApiModelProperty("0:老师未回复,1:老师已回复")
    @TableField("reply_status")
    private Integer replyStatus;

    @ApiModelProperty("老师回复时间")
    @TableField("reply_time")
    private LocalDateTime replyTime;

    @ApiModelProperty("0:未查看，1:已查看")
    @TableField("is_viewed")
    private Integer isViewed;

    @ApiModelProperty("sys_dict_info表中的dict_key")
    @TableField("dict_key")
    private Integer dictKey;

    @ApiModelProperty("sys_dict_info表中的dict_value类型的名称-冗余字段")
    @TableField("dict_value")
    private String dictValue;


}
