package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 挂件配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("common_pendant_config")
@ApiModel(value = "PendantConfig对象", description = "挂件配置表")
public class PendantConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("直播Id")
    @TableField("live_id")
    private Integer liveId;

    @ApiModelProperty("显示位置 默认是0  0 右侧；1左侧； 2左下方； 3右下方； 4功能区")
    @TableField("show_position")
    private Integer showPosition;

    @ApiModelProperty("挂件图片")
    @TableField("icon")
    private String icon;

    @ApiModelProperty("跳转类型： 1 不跳转；2商品；3 h5链接；4 图片;5课程；6 班级；7 作业；8 分享课程；9 分享课件；10 送好友；11 无；")
    @TableField("goto_type")
    private Integer gotoType;

    @ApiModelProperty("goto_type 对应的值")
    @TableField("goto_val")
    private String gotoVal;

    @ApiModelProperty("显示状态：1显示 2 隐藏")
    @TableField("show_status")
    private Integer showStatus;

    @ApiModelProperty("显示顺序")
    @TableField("show_sort")
    private String showSort;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("挂件名称")
    @TableField("pendant_name")
    private String pendantName;

    @ApiModelProperty("挂件的类型 1:课程类型 2:课件类型")
    @TableField("pendant_type")
    private Integer pendantType;

    @ApiModelProperty("挂件的值")
    @TableField("pendant_val")
    private String pendantVal;

    @ApiModelProperty("课程的id")
    @TableField("pendant_course_id")
    private Integer pendantCourseId;

    @ApiModelProperty("课件id(资源id)")
    @TableField("pendant_res_id")
    private Integer pendantResId;

    @ApiModelProperty("课件id(relId)")
    @TableField("course_info_res_id")
    private Integer courseInfoResId;


}
