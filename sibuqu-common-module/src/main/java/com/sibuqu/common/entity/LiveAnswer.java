package com.sibuqu.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 直播提问表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("common_live_answer")
@ApiModel(value = "LiveAnswer对象", description = "直播提问表")
public class LiveAnswer implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键信息 ")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("关联课程时间表ID")
    @TableField("course_timetable_id")
    private Integer courseTimetableId;

    @ApiModelProperty("内容")
    @TableField("answer")
    private String answer;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人名称")
    @TableField("create_user_name")
    private String createUserName;

    @ApiModelProperty("修改时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人名字")
    @TableField("update_user_name")
    private String updateUserName;

    @ApiModelProperty("修改人_用户id")
    @TableField("update_user_id")
    private String updateUserId;

    @ApiModelProperty("状态(0:隐藏1:显示)")
    @TableField("status")
    private Integer status;

    @ApiModelProperty("是否删除(0未删除，1已删除)")
    @TableField("delete_flag")
    private Integer deleteFlag;

}
